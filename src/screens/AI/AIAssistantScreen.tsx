import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Keyboard,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../components/common/ThemeProvider';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import TouchableScale from '../../components/ui/TouchableScale';
import PageHeader from '../../components/ui/PageHeader';
import { useNewsStore } from '../../store';
import { apiService } from '../../services/api';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const AIAssistantScreen: React.FC = () => {
  const { colors } = useTheme();
  const { articles } = useNewsStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const flatListRef = useRef<FlatList>(null);
  const typingAnim = useRef(new Animated.Value(0)).current;
  const styles = createStyles(colors);

  // 预设问题
  const suggestedQuestions = [
    '什么是DeFi？',
    'NFT的价值来源是什么？',
    '比特币和以太坊的区别？',
    '如何评估加密货币项目？',
    '区块链技术的应用场景？',
  ];

  useEffect(() => {
    // 初始欢迎消息
    const welcomeMessage: Message = {
      id: 'welcome',
      text: '你好！我是区块链AI助手，可以回答关于区块链、加密货币、DeFi、NFT等相关问题。有什么想了解的吗？',
      isUser: false,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);

    // 键盘事件监听
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
        setKeyboardHeight(e.endCoordinates.height);
        // 滚动到底部
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useEffect(() => {
    if (isTyping) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(typingAnim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(typingAnim, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      typingAnim.stopAnimation();
      typingAnim.setValue(0);
    }
  }, [isTyping]);

  const handleSendMessage = async (text?: string) => {
    const messageText = text || inputText.trim();
    if (!messageText) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: messageText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // 滚动到底部
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // 生成AI响应
      const aiResponse = await generateAIResponse(messageText);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);

      // 滚动到底部显示AI回复
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('AI响应生成失败:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: '抱歉，我现在无法回答您的问题。请稍后再试。',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      setIsTyping(false);
    }
  };

  const generateAIResponse = async (question: string): Promise<string> => {
    const lowerQuestion = question.toLowerCase();

    // 模拟思考时间
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 检查是否询问最新新闻
    if (lowerQuestion.includes('最新') || lowerQuestion.includes('新闻') || lowerQuestion.includes('资讯')) {
      const recentArticles = articles.slice(0, 3);
      if (recentArticles.length > 0) {
        let response = '📰 以下是最新的区块链资讯：\n\n';
        recentArticles.forEach((article, index) => {
          response += `${index + 1}. ${article.title}\n`;
          if (article.summary) {
            response += `   ${article.summary.substring(0, 100)}...\n\n`;
          }
        });
        response += '您想了解其中哪条新闻的详细信息吗？';
        return response;
      }
    }

    // 检查是否询问价格或市场
    if (lowerQuestion.includes('价格') || lowerQuestion.includes('市场') || lowerQuestion.includes('行情')) {
      try {
        const pricesResponse = await apiService.getPrices();
        if (pricesResponse.success && pricesResponse.data.length > 0) {
          let response = '💰 当前主要加密货币价格：\n\n';
          pricesResponse.data.slice(0, 5).forEach((price: any) => {
            response += `• ${price.name}: $${price.current_price}\n`;
          });
          response += '\n价格数据仅供参考，投资需谨慎！';
          return response;
        }
      } catch (error) {
        console.error('获取价格数据失败:', error);
      }
    }

    // 基于关键词的智能回答
    if (lowerQuestion.includes('defi')) {
      return 'DeFi（去中心化金融）是基于区块链技术的金融服务生态系统，它通过智能合约自动执行金融交易，无需传统金融中介。主要特点包括：\n\n• 去中心化：无需银行等中介机构\n• 透明性：所有交易记录公开可查\n• 可编程性：通过智能合约自动执行\n• 全球访问：24/7无地域限制\n\n常见的DeFi应用包括去中心化交易所(DEX)、借贷协议、流动性挖矿等。';
    }

    if (lowerQuestion.includes('nft')) {
      return 'NFT（非同质化代币）是一种基于区块链的数字资产，具有唯一性和不可替代性。NFT的价值来源包括：\n\n• 稀缺性：限量发行或独一无二\n• 实用性：游戏道具、会员权益等功能\n• 社区价值：持有者社区的认同感\n• 艺术价值：数字艺术作品的创意和美学\n• 投机价值：市场供需关系\n\n需要注意的是，NFT市场波动较大，投资需谨慎。';
    }

    if (lowerQuestion.includes('比特币') || lowerQuestion.includes('以太坊') || lowerQuestion.includes('btc') || lowerQuestion.includes('eth')) {
      return '比特币和以太坊是两个最重要的区块链项目，主要区别：\n\n**比特币(Bitcoin)**\n• 数字黄金，主要用作价值存储\n• 工作量证明(PoW)共识机制\n• 交易速度较慢，约7笔/秒\n• 编程功能有限\n\n**以太坊(Ethereum)**\n• 智能合约平台，支持DApps开发\n• 正在转向权益证明(PoS)\n• 交易速度更快，约15笔/秒\n• 支持复杂的智能合约\n\n两者都有各自的优势和应用场景。';
    }

    if (lowerQuestion.includes('投资') || lowerQuestion.includes('风险')) {
      return '💡 加密货币投资建议：\n\n**风险管理**\n• 只投资您能承受损失的资金\n• 分散投资，不要把鸡蛋放在一个篮子里\n• 设置止损点，控制风险\n\n**项目评估**\n• 研究项目的技术实力和团队背景\n• 了解项目的实际应用场景\n• 关注社区活跃度和生态发展\n\n**市场策略**\n• 长期持有优质项目\n• 避免情绪化交易\n• 定期关注市场动态\n\n⚠️ 加密货币市场波动极大，请谨慎投资！';
    }

    // 搜索相关文章
    if (articles.length > 0) {
      const relevantArticles = articles.filter(article =>
        article.title.toLowerCase().includes(lowerQuestion) ||
        article.summary?.toLowerCase().includes(lowerQuestion) ||
        article.tags?.some(tag => tag.toLowerCase().includes(lowerQuestion))
      );

      if (relevantArticles.length > 0) {
        let response = `我找到了 ${relevantArticles.length} 篇相关文章：\n\n`;
        relevantArticles.slice(0, 2).forEach((article, index) => {
          response += `${index + 1}. ${article.title}\n`;
          response += `   ${article.summary?.substring(0, 80)}...\n\n`;
        });
        response += '您想了解更多详情吗？';
        return response;
      }
    }

    return '这是一个很好的问题！区块链技术正在快速发展，涉及的概念和应用非常广泛。如果您想了解更具体的信息，可以问我关于：\n\n• 最新资讯和市场动态\n• 技术原理（共识机制、智能合约等）\n• 投资策略（风险管理、项目评估等）\n• 应用场景（DeFi、NFT、Web3等）\n• 价格行情和趋势分析\n\n请随时提出您感兴趣的具体问题！';
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <View style={[styles.messageContainer, item.isUser ? styles.userMessage : styles.aiMessage]}>
      {!item.isUser && (
        <View style={styles.aiAvatar}>
          <Ionicons name="sparkles" size={16} color={colors.primary} />
        </View>
      )}
      
      <View style={[styles.messageBubble, item.isUser ? styles.userBubble : styles.aiBubble]}>
        <Text style={[styles.messageText, item.isUser ? styles.userText : styles.aiText]}>
          {item.text}
        </Text>
        <Text style={styles.messageTime}>
          {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
      
      {item.isUser && (
        <View style={styles.userAvatar}>
          <Ionicons name="person" size={16} color={colors.textSecondary} />
        </View>
      )}
    </View>
  );

  const renderTypingIndicator = () => (
    <View style={[styles.messageContainer, styles.aiMessage]}>
      <View style={styles.aiAvatar}>
        <Ionicons name="sparkles" size={16} color={colors.primary} />
      </View>
      
      <View style={[styles.messageBubble, styles.aiBubble]}>
        <View style={styles.typingContainer}>
          <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
          <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
          <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
        </View>
      </View>
    </View>
  );

  const renderSuggestedQuestion = (question: string, index: number) => (
    <TouchableScale
      key={index}
      style={styles.suggestionChip}
      onPress={() => handleSendMessage(question)}
    >
      <Text style={styles.suggestionText}>{question}</Text>
    </TouchableScale>
  );

  return (
    <SafeAreaView style={styles.container}>
      <PageHeader
        title="AI助手"
        subtitle="区块链智能问答"
        rightComponent={
          <View style={styles.aiHeaderAvatar}>
            <Ionicons name="sparkles" size={20} color={colors.primary} />
          </View>
        }
      />

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
          ListFooterComponent={isTyping ? renderTypingIndicator : null}
        />

        {messages.length === 1 && (
          <View style={styles.suggestionsContainer}>
            <Text style={styles.suggestionsTitle}>常见问题</Text>
            <View style={styles.suggestionsGrid}>
              {suggestedQuestions.map(renderSuggestedQuestion)}
            </View>
          </View>
        )}

        <View style={[styles.inputContainer, { paddingBottom: Math.max(SPACING.md, keyboardHeight > 0 ? SPACING.xs : SPACING.md) }]}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              placeholder="输入您的问题..."
              placeholderTextColor={colors.textSecondary}
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
              onFocus={() => {
                // 输入框获得焦点时滚动到底部
                setTimeout(() => {
                  flatListRef.current?.scrollToEnd({ animated: true });
                }, 300);
              }}
            />
            <TouchableScale
              style={!inputText.trim() ? [styles.sendButton, styles.sendButtonDisabled] : styles.sendButton}
              onPress={() => handleSendMessage()}
              disabled={!inputText.trim()}
            >
              <Ionicons
                name="send"
                size={20}
                color={inputText.trim() ? '#FFFFFF' : colors.textSecondary}
              />
            </TouchableScale>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    aiHeaderAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      flex: 1,
    },
    messagesList: {
      flex: 1,
    },
    messagesContent: {
      paddingVertical: SPACING.md,
    },
    messageContainer: {
      flexDirection: 'row',
      marginVertical: SPACING.xs,
      paddingHorizontal: SPACING.md,
    },
    userMessage: {
      justifyContent: 'flex-end',
    },
    aiMessage: {
      justifyContent: 'flex-start',
    },
    aiAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
      alignSelf: 'flex-end',
    },
    userAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: SPACING.sm,
      alignSelf: 'flex-end',
    },
    messageBubble: {
      maxWidth: '75%',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.lg,
    },
    userBubble: {
      backgroundColor: colors.primary,
      borderBottomRightRadius: BORDER_RADIUS.sm,
    },
    aiBubble: {
      backgroundColor: colors.surface,
      borderBottomLeftRadius: BORDER_RADIUS.sm,
    },
    messageText: {
      fontSize: FONT_SIZES.base,
      lineHeight: 20,
    },
    userText: {
      color: '#FFFFFF',
    },
    aiText: {
      color: colors.text,
    },
    messageTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
      alignSelf: 'flex-end',
    },
    typingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    typingDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.textSecondary,
      marginHorizontal: 2,
    },
    suggestionsContainer: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.lg,
    },
    suggestionsTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.md,
    },
    suggestionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    suggestionChip: {
      backgroundColor: colors.surface,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      borderWidth: 1,
      borderColor: colors.border,
    },
    suggestionText: {
      fontSize: FONT_SIZES.sm,
      color: colors.text,
      fontWeight: FONT_WEIGHTS.medium,
    },
    inputContainer: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.xl,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
    },
    textInput: {
      flex: 1,
      fontSize: FONT_SIZES.base,
      color: colors.text,
      maxHeight: 100,
      paddingVertical: SPACING.xs,
    },
    sendButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: SPACING.sm,
    },
    sendButtonDisabled: {
      backgroundColor: colors.border,
    },
  });

export default AIAssistantScreen;
