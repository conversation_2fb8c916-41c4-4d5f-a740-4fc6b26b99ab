import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';

import { RootStackParamList, DataSourceItem } from '../../types';
import { useNewsStore } from '../../store';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import TouchableScale from '../../components/ui/TouchableScale';
import PullToRefresh from '../../components/ui/PullToRefresh';
import { apiService } from '../../services/api';
import { handleError } from '../../utils/errorHandler';

type TrendingListScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const TrendingListScreen: React.FC = () => {
  const navigation = useNavigation<TrendingListScreenNavigationProp>();
  const { colors } = useTheme();
  const [hotNewsArticles, setHotNewsArticles] = useState<DataSourceItem[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const styles = createStyles(colors);

  useEffect(() => {
    navigation.setOptions({
      title: '热门资讯',
      headerStyle: {
        backgroundColor: colors.background,
      },
      headerTintColor: colors.text,
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
      ),
    });

    // 初始加载数据
    loadHotNews();
  }, [navigation, colors]);

  // 加载热门资讯数据
  const loadHotNews = async (page = 1, isRefresh = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      }

      // 调用 /api/news 接口，dataType='news'，hasCover=true，limit=10，支持分页查询
      const response = await apiService.getArticles(
        page,
        10,
        undefined,
        ['news'],
        true, // 必须有封面
        undefined,
        'publishTime',
        'desc'
      );

      if (response.success) {
        if (isRefresh || page === 1) {
          setHotNewsArticles(response.data.items);
          setCurrentPage(1);
        } else {
          setHotNewsArticles(prev => [...prev, ...response.data.items]);
        }

        setCurrentPage(page);
        setHasMore(response.data.hasMore || false);
      }
    } catch (error) {
      console.error('加载热门资讯失败:', error);
      handleError(error, '加载热门资讯');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadHotNews(1, true);
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreNews = async () => {
    if (!hasMore || loading) return;
    await loadHotNews(currentPage + 1, false);
  };

  const navigateToArticle = (articleId: string) => {
    navigation.navigate('ArticleDetail', { articleId });
  };

  const renderTrendingArticle = ({ item: article, index }: { item: DataSourceItem; index: number }) => (
    <TouchableScale
      style={styles.articleCard}
      onPress={() => navigateToArticle(article.id)}
    >
      <View style={styles.rankContainer}>
        <Text style={styles.rankNumber}>{index + 1}</Text>
      </View>

      <Image
        source={{ uri: article.cover || 'https://placehold.co/80x60' }}
        style={styles.articleImage}
      />

      <View style={styles.articleContent}>
        <Text style={styles.articleTitle} numberOfLines={2}>
          {article.title}
        </Text>
        <Text style={styles.articleSummary} numberOfLines={2}>
          {article.description || article.content || ''}
        </Text>
        <View style={styles.articleMeta}>
          <Text style={styles.articleCategory}>{article.category?.name || article.dataType}</Text>
          <Text style={styles.articleTime}>{new Date(article.publishTime).toLocaleDateString()}</Text>
        </View>
      </View>

      <View style={styles.actionButton}>
        <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
      </View>
    </TouchableScale>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* <View style={styles.header}>
        <View style={styles.headerContent}>
          <Ionicons name="flame" size={24} color={colors.warning} />
          <Text style={styles.headerTitle}>热门资讯排行</Text>
          <View style={styles.hotBadge}>
            <Text style={styles.hotBadgeText}>HOT</Text>
          </View>
        </View>
        <Text style={styles.headerSubtitle}>实时更新最热门的区块链资讯</Text>
      </View> */}

      <FlatList
        data={hotNewsArticles}
        renderItem={renderTrendingArticle}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
        onEndReached={loadMoreNews}
        onEndReachedThreshold={0.1}
        ListFooterComponent={
          loading && hasMore ? (
            <View style={styles.loadingFooter}>
              <Text style={styles.loadingText}>加载中...</Text>
            </View>
          ) : null
        }
        ListEmptyComponent={
          !loading ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="flame-outline" size={48} color={colors.textSecondary} />
              <Text style={styles.emptyText}>暂无热门资讯</Text>
            </View>
          ) : null
        }
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerButton: {
      padding: SPACING.sm,
      marginLeft: SPACING.xs,
    },
    header: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.lg,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    headerTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginLeft: SPACING.sm,
    },
    hotBadge: {
      backgroundColor: colors.warning,
      paddingHorizontal: SPACING.sm,
      paddingVertical: 2,
      borderRadius: BORDER_RADIUS.sm,
      marginLeft: SPACING.sm,
    },
    hotBadgeText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    headerSubtitle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: 32,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingVertical: SPACING.md,
    },
    articleCard: {
      flexDirection: 'row',
      alignItems: 'flex-start', // 改为顶部对齐，提供更好的视觉层次
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.sm, // 减少底部边距，使排版更紧凑
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      paddingVertical: SPACING.sm, // 减少垂直内边距
      paddingHorizontal: SPACING.md,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      minHeight: 72, // 减少最小高度，使布局更紧凑
    },
    rankContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: SPACING.sm, // 减少右边距
      width: 32, // 优化宽度
      height: 32, // 添加固定高度，创建圆形背景
      backgroundColor: colors.primary + '10', // 添加淡色背景
      borderRadius: 16, // 圆形背景
      marginTop: SPACING.xs, // 添加顶部边距以对齐
    },
    rankNumber: {
      fontSize: FONT_SIZES.base, // 稍微减小字体大小
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.primary,
    },
    trendingIndicator: {
      backgroundColor: colors.success + '20',
      borderRadius: BORDER_RADIUS.sm,
      padding: 2,
    },
    articleImage: {
      width: 64, // 稍微减小图片尺寸
      height: 48,
      borderRadius: BORDER_RADIUS.md,
      marginRight: SPACING.sm, // 减少右边距
      marginTop: SPACING.xs, // 添加顶部边距以对齐
    },
    articleContent: {
      flex: 1,
    },
    articleTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 18, // 减少行高，使布局更紧凑
    },
    articleSummary: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
      lineHeight: 18,
    },
    articleMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    articleCategory: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    articleTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    actionButton: {
      padding: SPACING.xs,
      marginLeft: SPACING.sm,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: SPACING.xl * 2,
    },
    emptyText: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
    loadingFooter: {
      paddingVertical: SPACING.lg,
      alignItems: 'center',
    },
    loadingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
  });

export default TrendingListScreen;
