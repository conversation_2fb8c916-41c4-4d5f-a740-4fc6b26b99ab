import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Ionicons } from "@expo/vector-icons";

import { RootStackParamList, DataSourceItem } from "../../types";
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from "../../constants";
import { useTheme } from "../../components/common/ThemeProvider";
import PullToRefresh from "../../components/ui/PullToRefresh";
import { apiService } from "../../services/api";
import { handleError } from "../../utils/errorHandler";

type TwitterListScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;

// 格式化时间显示
const formatTimestamp = (publishTime: string): string => {
  try {
    const date = new Date(publishTime);
    if (isNaN(date.getTime())) {
      return "未知时间";
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "刚刚";
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return date.toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
    });
  } catch (error) {
    return "未知时间";
  }
};

const TwitterListScreen: React.FC = () => {
  const navigation = useNavigation<TwitterListScreenNavigationProp>();
  const { colors } = useTheme();
  const [tweets, setTweets] = useState<DataSourceItem[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [expandedPosts, setExpandedPosts] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const styles = createStyles(colors);

  useEffect(() => {
    navigation.setOptions({
      title: "推特观点",
      headerStyle: {
        backgroundColor: colors.background,
      },
      headerTintColor: colors.text,
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
      ),
    });

    // 初始加载数据
    loadTweets();
  }, [navigation, colors]);

  // 加载推特数据
  const loadTweets = async (page = 1, isRefresh = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      }

      const response = await apiService.getTweets(page, 100, undefined);

      if (response.success) {
        if (isRefresh || page === 1) {
          setTweets(response.data.items);
          setCurrentPage(1);
        } else {
          setTweets((prev) => [...prev, ...response.data.items]);
        }

        setCurrentPage(page);
        setHasMore(response.data.hasMore || false);
      }
    } catch (error) {
      console.error("加载推特失败:", error);
      handleError(error, "加载推特列表");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadTweets(1, true);
    } catch (error) {
      console.error("刷新推特失败:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreTweets = async () => {
    if (!hasMore || loading) return;
    await loadTweets(currentPage + 1, false);
  };

  const handlePostPress = (post: DataSourceItem) => {
    console.log(`点击推特帖子: `, post);
    // 这里可以实现跳转到推特详情或外部链接
  };

  const togglePostExpansion = (postId: string) => {
    setExpandedPosts((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(postId)) {
        newSet.delete(postId);
      } else {
        newSet.add(postId);
      }
      return newSet;
    });
  };

  const isPostExpanded = (postId: string) => expandedPosts.has(postId);

  const shouldShowExpandButton = (content: string) => content.length > 120;

  const renderTwitterPost = ({ item: post }: { item: DataSourceItem }) => {
    const username = post.author || post.title?.replace("@", "") || "Unknown";
    const handle = post.title?.startsWith("@") ? post.title : `@${username}`;
    const content = post.tweetText || post.content || "";
    const timestamp = formatTimestamp(post.publishTime);

    return (
      <View style={styles.postCard}>
        <View style={styles.postHeader}>
          <View style={styles.authorInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {username.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.authorDetails}>
              <View style={styles.authorName}>
                <Text style={styles.authorNameText}>{username}</Text>
              </View>
              <Text style={styles.authorHandle}>{handle}</Text>
            </View>
          </View>
          <Text style={styles.timestamp}>{timestamp}</Text>
        </View>

        <View style={styles.contentContainer}>
          <TouchableOpacity
            onPress={() =>
              shouldShowExpandButton(content)
                ? togglePostExpansion(post.id)
                : handlePostPress(post)
            }
            activeOpacity={0.7}
          >
            <Text
              style={styles.postContent}
              numberOfLines={isPostExpanded(post.id) ? undefined : 3}
              ellipsizeMode="tail"
            >
              {content}
            </Text>
          </TouchableOpacity>
          {shouldShowExpandButton(content) && (
            <TouchableOpacity
              onPress={() => togglePostExpansion(post.id)}
              style={styles.expandButton}
            >
              <Text style={styles.expandButtonText}>
                {isPostExpanded(post.id) ? "收起" : "展开"}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.postStats}>
          <View style={styles.statItem}>
            <Ionicons
              name="heart-outline"
              size={16}
              color={colors.textSecondary}
            />
            <Text style={styles.statText}>{post.engagement?.likes || 0}</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons
              name="repeat-outline"
              size={16}
              color={colors.textSecondary}
            />
            <Text style={styles.statText}>{post.engagement?.shares || 0}</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons
              name="chatbubble-outline"
              size={16}
              color={colors.textSecondary}
            />
            <Text style={styles.statText}>评论</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons
              name="share-outline"
              size={16}
              color={colors.textSecondary}
            />
            <Text style={styles.statText}>分享</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={tweets}
        renderItem={renderTwitterPost}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
        onEndReached={loadMoreTweets}
        onEndReachedThreshold={0.1}
        ListFooterComponent={
          loading && hasMore ? (
            <View style={styles.loadingFooter}>
              <Text style={styles.loadingText}>加载中...</Text>
            </View>
          ) : null
        }
        ListEmptyComponent={
          !loading ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="logo-twitter" size={48} color="#1DA1F2" />
              <Text style={styles.emptyText}>暂无推特观点</Text>
            </View>
          ) : null
        }
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerButton: {
      padding: SPACING.sm,
      marginLeft: SPACING.xs,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingVertical: SPACING.md,
    },
    postCard: {
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.md,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING.md,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    postHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: SPACING.md,
    },
    authorInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: "#1DA1F2",
      justifyContent: "center",
      alignItems: "center",
      marginRight: SPACING.sm,
    },
    avatarText: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.bold,
      color: "#FFFFFF",
    },
    authorDetails: {
      flex: 1,
    },
    authorName: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 2,
    },
    authorNameText: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginRight: SPACING.xs,
    },
    authorHandle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    timestamp: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    contentContainer: {
      marginBottom: SPACING.md,
    },
    postContent: {
      fontSize: FONT_SIZES.base,
      color: colors.text,
      lineHeight: 20,
    },
    expandButton: {
      alignSelf: "flex-start",
      marginTop: SPACING.xs,
      paddingVertical: SPACING.xs,
      paddingHorizontal: SPACING.sm,
    },
    expandButtonText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    postStats: {
      flexDirection: "row",
      justifyContent: "space-around",
      paddingTop: SPACING.sm,
      borderTopWidth: 1,
      borderTopColor: colors.borderLight,
    },
    statItem: {
      flexDirection: "row",
      alignItems: "center",
    },
    statText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: SPACING.xl * 2,
    },
    emptyText: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
    loadingFooter: {
      paddingVertical: SPACING.lg,
      alignItems: "center",
    },
    loadingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
  });

export default TwitterListScreen;
