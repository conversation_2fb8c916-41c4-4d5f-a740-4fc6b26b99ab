import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RootStackParamList, NewsCategory, NewsArticle } from '../../types';
import { useAppStore, useNewsStore } from '../../store';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import TouchableScale from '../../components/ui/TouchableScale';

type CategoriesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const CategoriesScreen: React.FC = () => {
  const navigation = useNavigation<CategoriesScreenNavigationProp>();
  const { colors } = useTheme();
  const { categories, getArticlesByCategory } = useNewsStore();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const styles = createStyles(colors);

  const navigateToArticle = (articleId: string) => {
    navigation.navigate('ArticleDetail', { articleId });
  };

  const renderCategoryItem = ({ item }: { item: NewsCategory }) => {
    const isSelected = selectedCategory === item.id;
    const articlesCount = getArticlesByCategory(item.id).length;

    return (
      <TouchableScale
        style={isSelected ? [styles.categoryCard, styles.categoryCardSelected] : styles.categoryCard}
        onPress={() => setSelectedCategory(isSelected ? null : item.id)}
      >
        <View style={[styles.categoryIcon, { backgroundColor: item.color }]}>
          <Ionicons name={item.icon as any} size={24} color="white" />
        </View>
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryName}>{item.name}</Text>
          <Text style={styles.categoryCount}>{articlesCount} 篇文章</Text>
        </View>
        <Ionicons
          name={isSelected ? 'chevron-up' : 'chevron-down'}
          size={20}
          color={colors.textSecondary}
        />
      </TouchableScale>
    );
  };

  const renderArticleItem = ({ item }: { item: NewsArticle }) => (
    <TouchableScale
      style={styles.articleCard}
      onPress={() => navigateToArticle(item.id)}
    >
      <Image
        source={{ uri: item.cover || 'https://via.placeholder.com/100x80' }}
        style={styles.articleImage}
      />
      <View style={styles.articleContent}>
        <Text style={styles.articleTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={styles.articleSummary} numberOfLines={2}>
          {item.summary}
        </Text>
        <View style={styles.articleMeta}>
          <Text style={styles.articleAuthor}>{item.author}</Text>
          <Text style={styles.articleTime}>{item.publishTime}</Text>
        </View>
      </View>
    </TouchableScale>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>新闻分类</Text>
    </View>
  );

  const selectedCategoryArticles = selectedCategory
    ? getArticlesByCategory(selectedCategory)
    : [];

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      <FlatList
        data={categories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
      
      {selectedCategory && (
        <View style={styles.articlesSection}>
          <Text style={styles.articlesSectionTitle}>
            {categories.find(c => c.id === selectedCategory)?.name} 文章
          </Text>
          <FlatList
            data={selectedCategoryArticles}
            renderItem={renderArticleItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            style={styles.articlesList}
          />
        </View>
      )}
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: FONT_SIZES['2xl'],
      fontWeight: 'bold',
      color: colors.text,
    },
    listContent: {
      paddingVertical: SPACING.sm,
    },
    categoryCard: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      marginHorizontal: SPACING.md,
      marginVertical: SPACING.xs,
      padding: SPACING.md,
      borderRadius: 12,
    },
    categoryCardSelected: {
      borderWidth: 2,
      borderColor: colors.primary,
    },
    categoryIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    categoryInfo: {
      flex: 1,
    },
    categoryName: {
      fontSize: FONT_SIZES.md,
      fontWeight: '600',
      color: colors.text,
      marginBottom: SPACING.xs,
    },
    categoryCount: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    articlesSection: {
      flex: 1,
      backgroundColor: colors.surface,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      paddingTop: SPACING.md,
    },
    articlesSectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: 'bold',
      color: colors.text,
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.sm,
    },
    articlesList: {
      flex: 1,
    },
    articleCard: {
      flexDirection: 'row',
      backgroundColor: colors.background,
      marginHorizontal: SPACING.md,
      marginVertical: SPACING.xs,
      borderRadius: 12,
      overflow: 'hidden',
    },
    articleImage: {
      width: 100,
      height: 80,
    },
    articleContent: {
      flex: 1,
      padding: SPACING.md,
    },
    articleTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: '600',
      color: colors.text,
      marginBottom: SPACING.xs,
    },
    articleSummary: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
    },
    articleMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    articleAuthor: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: '500',
    },
    articleTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
  });

export default CategoriesScreen;
