import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../components/common/ThemeProvider';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import TouchableScale from '../../components/ui/TouchableScale';
import Card from '../../components/ui/Card';
import PageHeader from '../../components/ui/PageHeader';
import PullToRefresh from '../../components/ui/PullToRefresh';
import { useNewsStore } from '../../store';
import { apiService } from '../../services/api';

const { width: screenWidth } = Dimensions.get('window');

interface TrendingTopic {
  id: string;
  title: string;
  change: number;
  posts: number;
}

interface MarketData {
  symbol: string;
  name: string;
  price: string;
  change: number;
  volume: string;
}

interface ExpertOpinion {
  id: string;
  author: string;
  title: string;
  content: string;
  avatar: string;
  verified: boolean;
  likes: number;
  time: string;
}

interface EducationContent {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: string;
  image: string;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  rating: number;
  downloads: string;
}

const DiscoverScreen: React.FC = () => {
  const { colors } = useTheme();
  const { articles } = useNewsStore();
  const [trendingArticles, setTrendingArticles] = useState<any[]>([]);
  const [hotTweets, setHotTweets] = useState<any[]>([]);
  const [priceData, setPriceData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const styles = createStyles(colors);

  useEffect(() => {
    loadDiscoverData();
  }, []);

  const loadDiscoverData = async () => {
    setLoading(true);
    try {
      // 并行加载数据
      const [trendingResponse, tweetsResponse, pricesResponse] = await Promise.all([
        apiService.getTrendingArticles(5),
        apiService.getHotTweets(3),
        apiService.getPrices(),
      ]);

      if (trendingResponse.success) {
        setTrendingArticles(trendingResponse.data);
      }

      if (tweetsResponse.success) {
        setHotTweets(tweetsResponse.data);
      }

      if (pricesResponse.success) {
        setPriceData(pricesResponse.data.slice(0, 4));
      }
    } catch (error) {
      console.error('加载发现页数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadDiscoverData();
    } catch (error) {
      console.error('刷新发现页数据失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 模拟热门话题数据
  const trendingTopics: TrendingTopic[] = [
    { id: '1', title: 'Bitcoin ETF', change: 45.2, posts: 1234 },
    { id: '2', title: 'Ethereum Merge', change: 23.8, posts: 892 },
    { id: '3', title: 'DeFi Summer', change: 18.5, posts: 567 },
    { id: '4', title: 'NFT Marketplace', change: -12.3, posts: 445 },
  ];

  // 模拟专家观点数据
  const expertOpinions: ExpertOpinion[] = [
    {
      id: '1',
      author: 'Vitalik Buterin',
      title: '以太坊的未来发展方向',
      content: '随着以太坊2.0的推进，我们将看到更高的可扩展性和更低的能耗...',
      avatar: 'https://via.placeholder.com/40x40',
      verified: true,
      likes: 2341,
      time: '2小时前',
    },
    {
      id: '2',
      author: 'Michael Saylor',
      title: 'Bitcoin作为数字黄金的价值',
      content: 'Bitcoin不仅是一种投资工具，更是对抗通胀的最佳选择...',
      avatar: 'https://via.placeholder.com/40x40',
      verified: true,
      likes: 1876,
      time: '4小时前',
    },
  ];

  // 模拟教育内容数据
  const educationContent: EducationContent[] = [
    {
      id: '1',
      title: '区块链基础知识',
      description: '从零开始学习区块链技术的基本概念和原理',
      category: '基础教程',
      difficulty: 'beginner',
      duration: '30分钟',
      image: 'https://via.placeholder.com/120x80',
    },
    {
      id: '2',
      title: 'DeFi协议深度解析',
      description: '深入了解去中心化金融协议的工作机制',
      category: '进阶教程',
      difficulty: 'advanced',
      duration: '45分钟',
      image: 'https://via.placeholder.com/120x80',
    },
  ];

  // 模拟工具推荐数据
  const recommendedTools: Tool[] = [
    {
      id: '1',
      name: 'MetaMask',
      description: '最受欢迎的以太坊钱包',
      category: '钱包',
      icon: 'wallet-outline',
      rating: 4.8,
      downloads: '10M+',
    },
    {
      id: '2',
      name: 'DeFiPulse',
      description: 'DeFi数据分析平台',
      category: '分析工具',
      icon: 'analytics-outline',
      rating: 4.6,
      downloads: '500K+',
    },
  ];

  // 渲染热门话题模块
  // 渲染专家观点模块
  const renderExpertOpinions = () => (
    <Card style={styles.moduleCard} variant="elevated" padding="lg">
      <View style={styles.moduleHeader}>
        <View style={styles.moduleHeaderLeft}>
          <Ionicons name="people" size={20} color={colors.primary} />
          <Text style={styles.moduleTitle}>专家观点</Text>
        </View>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>查看全部</Text>
        </TouchableOpacity>
      </View>

      {expertOpinions.map((opinion) => (
        <TouchableScale key={opinion.id} style={styles.opinionItem}>
          <View style={styles.opinionHeader}>
            <Image source={{ uri: opinion.avatar }} style={styles.expertAvatar} />
            <View style={styles.expertInfo}>
              <View style={styles.expertName}>
                <Text style={styles.expertNameText}>{opinion.author}</Text>
                {opinion.verified && (
                  <Ionicons name="checkmark-circle" size={14} color={colors.primary} />
                )}
              </View>
              <Text style={styles.opinionTime}>{opinion.time}</Text>
            </View>
          </View>
          <Text style={styles.opinionTitle}>{opinion.title}</Text>
          <Text style={styles.opinionContent} numberOfLines={2}>{opinion.content}</Text>
          <View style={styles.opinionStats}>
            <View style={styles.statItem}>
              <Ionicons name="heart-outline" size={14} color={colors.textSecondary} />
              <Text style={styles.statText}>{opinion.likes}</Text>
            </View>
          </View>
        </TouchableScale>
      ))}
    </Card>
  );

  const renderTrendingTopics = () => (
    <Card style={styles.moduleCard} variant="elevated" padding="lg">
      <View style={styles.moduleHeader}>
        <View style={styles.moduleHeaderLeft}>
          <Ionicons name="flame" size={20} color={colors.warning} />
          <Text style={styles.moduleTitle}>热门话题</Text>
        </View>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>查看全部</Text>
        </TouchableOpacity>
      </View>

      {trendingTopics.slice(0, 3).map((topic, index) => (
        <TouchableScale key={topic.id} style={styles.topicItem}>
          <View style={styles.topicRank}>
            <Text style={styles.rankNumber}>{index + 1}</Text>
          </View>
          <View style={styles.topicContent}>
            <Text style={styles.topicTitle}>{topic.title}</Text>
            <Text style={styles.topicPosts}>{topic.posts} 讨论</Text>
          </View>
          <View style={styles.topicChange}>
            <Ionicons
              name={topic.change > 0 ? 'trending-up' : 'trending-down'}
              size={14}
              color={topic.change > 0 ? colors.success : colors.error}
            />
            <Text style={[
              styles.changeText,
              { color: topic.change > 0 ? colors.success : colors.error }
            ]}>
              {topic.change > 0 ? '+' : ''}{topic.change}%
            </Text>
          </View>
        </TouchableScale>
      ))}
    </Card>
  );

  // 渲染教育内容模块
  const renderEducationContent = () => (
    <Card style={styles.moduleCard} variant="elevated" padding="lg">
      <View style={styles.moduleHeader}>
        <View style={styles.moduleHeaderLeft}>
          <Ionicons name="school" size={20} color={colors.secondary} />
          <Text style={styles.moduleTitle}>教育内容</Text>
        </View>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>查看全部</Text>
        </TouchableOpacity>
      </View>

      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {educationContent.map((content) => (
          <TouchableScale key={content.id} style={styles.educationCard}>
            <Image source={{ uri: content.image }} style={styles.educationImage} />
            <View style={styles.educationContent}>
              <View style={styles.educationMeta}>
                <Text style={styles.educationCategory}>{content.category}</Text>
                <View style={[styles.difficultyBadge, styles[`difficulty${content.difficulty.charAt(0).toUpperCase() + content.difficulty.slice(1)}`]]}>
                  <Text style={styles.difficultyText}>
                    {content.difficulty === 'beginner' ? '初级' : content.difficulty === 'intermediate' ? '中级' : '高级'}
                  </Text>
                </View>
              </View>
              <Text style={styles.educationTitle} numberOfLines={2}>{content.title}</Text>
              <Text style={styles.educationDescription} numberOfLines={2}>{content.description}</Text>
              <View style={styles.educationFooter}>
                <Ionicons name="time-outline" size={12} color={colors.textSecondary} />
                <Text style={styles.educationDuration}>{content.duration}</Text>
              </View>
            </View>
          </TouchableScale>
        ))}
      </ScrollView>
    </Card>
  );

  // 渲染工具推荐模块
  const renderToolRecommendations = () => (
    <Card style={styles.moduleCard} variant="elevated" padding="lg">
      <View style={styles.moduleHeader}>
        <View style={styles.moduleHeaderLeft}>
          <Ionicons name="construct" size={20} color={colors.success} />
          <Text style={styles.moduleTitle}>工具推荐</Text>
        </View>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>查看全部</Text>
        </TouchableOpacity>
      </View>

      {recommendedTools.map((tool) => (
        <TouchableScale key={tool.id} style={styles.toolItem}>
          <View style={styles.toolIcon}>
            <Ionicons name={tool.icon as any} size={24} color={colors.primary} />
          </View>
          <View style={styles.toolContent}>
            <View style={styles.toolHeader}>
              <Text style={styles.toolName}>{tool.name}</Text>
              <Text style={styles.toolCategory}>{tool.category}</Text>
            </View>
            <Text style={styles.toolDescription} numberOfLines={2}>{tool.description}</Text>
            <View style={styles.toolStats}>
              <View style={styles.toolRating}>
                <Ionicons name="star" size={12} color={colors.warning} />
                <Text style={styles.ratingText}>{tool.rating}</Text>
              </View>
              <Text style={styles.downloadText}>{tool.downloads} 下载</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.installButton}>
            <Text style={styles.installText}>安装</Text>
          </TouchableOpacity>
        </TouchableScale>
      ))}
    </Card>
  );





  return (
    <SafeAreaView style={styles.container}>
      <PageHeader
        title="发现"
        subtitle="探索区块链世界的精彩内容"
      />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderTrendingTopics()}
        {renderExpertOpinions()}
        {renderEducationContent()}
        {renderToolRecommendations()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    tabContainer: {
      flexDirection: 'row',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      backgroundColor: colors.surface,
    },
    tabButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      marginHorizontal: SPACING.xs,
    },
    activeTabButton: {
      backgroundColor: colors.primary + '20',
    },
    tabText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    activeTabText: {
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.semibold,
    },
    content: {
      flex: 1,
      paddingVertical: SPACING.md,
    },
    moduleCard: {
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.lg,
    },
    moduleHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.md,
    },
    moduleHeaderLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    moduleTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginLeft: SPACING.sm,
    },
    seeAllText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    // 热门话题样式
    topicItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    topicRank: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    rankNumber: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.primary,
    },
    topicContent: {
      flex: 1,
    },
    topicTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: 2,
    },
    topicPosts: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    topicChange: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    changeText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      marginLeft: SPACING.xs,
    },
    // 专家观点样式
    opinionItem: {
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    opinionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    expertAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      marginRight: SPACING.sm,
    },
    expertInfo: {
      flex: 1,
    },
    expertName: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 2,
    },
    expertNameText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginRight: SPACING.xs,
    },
    opinionTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    opinionTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
    },
    opinionContent: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
      marginBottom: SPACING.sm,
    },
    opinionStats: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    statText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    // 教育内容样式
    educationCard: {
      width: 200,
      marginRight: SPACING.md,
      backgroundColor: colors.surfaceElevated,
      borderRadius: BORDER_RADIUS.lg,
      overflow: 'hidden',
    },
    educationImage: {
      width: '100%',
      height: 100,
    },
    educationContent: {
      padding: SPACING.md,
    },
    educationMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    educationCategory: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    difficultyBadge: {
      paddingHorizontal: SPACING.xs,
      paddingVertical: 2,
      borderRadius: BORDER_RADIUS.sm,
    },
    difficultyBeginner: {
      backgroundColor: colors.success + '20',
    },
    difficultyIntermediate: {
      backgroundColor: colors.warning + '20',
    },
    difficultyAdvanced: {
      backgroundColor: colors.error + '20',
    },
    difficultyText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.medium,
    },
    educationTitle: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
    },
    educationDescription: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      lineHeight: 16,
      marginBottom: SPACING.sm,
    },
    educationFooter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    educationDuration: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    // 工具推荐样式
    toolItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    toolIcon: {
      width: 48,
      height: 48,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    toolContent: {
      flex: 1,
    },
    toolHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    toolName: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
    },
    toolCategory: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    toolDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
      marginBottom: SPACING.sm,
    },
    toolStats: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    toolRating: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    ratingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.text,
      marginLeft: SPACING.xs,
    },
    downloadText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    installButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.md,
      marginLeft: SPACING.sm,
    },
    installText: {
      fontSize: FONT_SIZES.sm,
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.semibold,
    },


  });

export default DiscoverScreen;
