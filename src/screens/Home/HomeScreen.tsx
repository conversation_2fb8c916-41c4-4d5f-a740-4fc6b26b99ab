import React, { useEffect, useState } from "react";
import { View, Text, ScrollView, StyleSheet, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

import { RootStackParamList, DataSourceItem } from "../../types";
import { useNewsStore } from "../../store";
import { apiService } from "../../services/api";
import { handleError } from "../../utils/errorHandler";
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from "../../constants";
import { useTheme } from "../../components/common/ThemeProvider";
import PullToRefresh from "../../components/ui/PullToRefresh";
import InlineSearchBar from "../../components/ui/InlineSearchBar";
import FeaturedSection from "../../components/home/<USER>";
import TwitterSection from "../../components/home/<USER>";
import TrendingSection from "../../components/home/<USER>";
import PageHeader from "../../components/ui/PageHeader";

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { colors } = useTheme();
  const { articles, loadArticles, refreshArticles, searchArticles } =
    useNewsStore();

  const [refreshing, setRefreshing] = useState(false);
  const [featuredArticles, setFeaturedArticles] = useState<DataSourceItem[]>(
    []
  );
  const [hotNewsArticles, setHotNewsArticles] = useState<DataSourceItem[]>([]);
  const [hotTweets, setHotTweets] = useState<DataSourceItem[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([
    "比特币突破新高",
    "DeFi总锁仓价值",
    "NFT艺术品",
  ]);
  const [loading, setLoading] = useState({
    featured: false,
    hotNews: false,
    tweets: false,
  });

  const styles = createStyles(colors);

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      // 并行加载所有数据
      await Promise.all([
        loadArticles(1), // 加载文章列表
        loadFeaturedArticles(), // 加载精选文章
        loadHotNewsArticles(), // 加载热门资讯
        loadHotTweets(), // 加载热门推特
      ]);
    } catch (error) {
      console.error("初始化数据失败:", error);
      handleError(error, "初始化数据");
    }
  };

  // 加载精选文章
  const loadFeaturedArticles = async () => {
    try {
      setLoading((prev) => ({ ...prev, featured: true }));
      const response = await apiService.getFeaturedArticles(5);
      if (response.success) {
        setFeaturedArticles(response.data);
      }
    } catch (error) {
      console.error("加载精选文章失败:", error);
      const appError = handleError(error, "加载精选文章");
      // 使用默认数据作为降级
      setFeaturedArticles(articles.slice(0, 5));
    } finally {
      setLoading((prev) => ({ ...prev, featured: false }));
    }
  };

  // 加载热门资讯
  const loadHotNewsArticles = async () => {
    try {
      setLoading((prev) => ({ ...prev, hotNews: true }));
      const response = await apiService.getHotNews(5);
      if (response.success) {
        setHotNewsArticles(response.data);
      }
    } catch (error) {
      console.error("加载热门资讯失败:", error);
      const appError = handleError(error, "加载热门资讯");
      // 使用默认数据作为降级
      setHotNewsArticles(articles.slice(0, 5));
    } finally {
      setLoading((prev) => ({ ...prev, hotNews: false }));
    }
  };

  const loadHotTweets = async () => {
    try {
      setLoading((prev) => ({ ...prev, tweets: true }));
      const response = await apiService.getHotTweets(5);

      if (response.success) {
        setHotTweets(response.data);
      }
    } catch (error) {
      console.error("加载热门推特失败:", error);
      const appError = handleError(error, "加载热门推特");
      // 如果API失败，使用空数组
      setHotTweets([]);
    } finally {
      setLoading((prev) => ({ ...prev, tweets: false }));
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        refreshArticles(),
        loadFeaturedArticles(),
        loadHotNewsArticles(),
        loadHotTweets(),
      ]);
    } catch (error) {
      console.error("刷新数据失败:", error);
      handleError(error, "刷新数据");
    } finally {
      setRefreshing(false);
    }
  };

  const navigateToArticle = (articleId: string) => {
    navigation.navigate("ArticleDetail", { articleId });
  };

  const handleSearch = async (query: string) => {
    // 添加到搜索历史
    if (!searchHistory.includes(query)) {
      setSearchHistory((prev) => [query, ...prev.slice(0, 9)]);
    }

    // 执行搜索
    await searchArticles(query);
  };

  const clearSearchHistory = () => {
    setSearchHistory([]);
  };

  const handleSeeAllPress = (section: string) => {
    switch (section) {
      case "featured":
        // 跳转到精选文章列表页面，使用特定的筛选条件
        navigation.navigate("FeaturedList");
        break;
      case "twitter":
        // 跳转到推特列表页面，调用 /api/tweets 接口，limit=10，支持分页查询
        navigation.navigate("TwitterList");
        break;
      case "trending":
        // 跳转到热门资讯列表页面，调用 /api/news 接口，limit=10，支持分页查询
        navigation.navigate("TrendingList");
        break;
      default:
        console.log(`查看全部 ${section}`);
    }
  };

  const handleTwitterPostPress = (post: DataSourceItem) => {
    // 处理推特帖子点击
    console.log(`点击推特帖子: `, post);
    // 可以在这里添加跳转到推特详情或外部链接的逻辑
  };

  // 分类文章数据
  const getCurrentFeaturedArticles = () =>
    featuredArticles.length > 0 ? featuredArticles : articles.slice(0, 5);
  const getCurrentHotNewsArticles = () =>
    hotNewsArticles.length > 0 ? hotNewsArticles : articles.slice(0, 5);
  const getCurrentHotTweets = () => hotTweets;
  const [isExpanded, setIsExpanded] = useState(false);
  const renderHeader = () => (
    <PageHeader
      title={isExpanded ? "" : "区块链资讯"}
      rightComponent={
        <InlineSearchBar
          onSearch={handleSearch}
          searchHistory={searchHistory}
          onClearHistory={clearSearchHistory}
          style={styles.searchBar}
          isExpanded={isExpanded}
          setIsExpanded={setIsExpanded}
        />
      }
    />
  );

  const renderContent = () => (
    <View>
      <FeaturedSection
        articles={getCurrentFeaturedArticles()}
        onArticlePress={navigateToArticle}
        onSeeAllPress={() => handleSeeAllPress("featured")}
      />

      <TwitterSection
        posts={getCurrentHotTweets()}
        onPostPress={handleTwitterPostPress}
        onSeeAllPress={() => handleSeeAllPress("twitter")}
      />

      <TrendingSection
        articles={getCurrentHotNewsArticles()}
        onArticlePress={navigateToArticle}
        onSeeAllPress={() => handleSeeAllPress("trending")}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
        decelerationRate="normal"
        scrollEventThrottle={16}
        bounces={true}
        bouncesZoom={false}
        overScrollMode="auto"
      >
        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    searchBar: {
      // 移除flex: 1，让搜索按钮保持固定大小
    },
    searchButton: {
      padding: SPACING.sm,
    },
    listContent: {
      paddingBottom: SPACING.xl,
    },
    section: {
      marginVertical: SPACING.md,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: "bold",
      color: colors.text,
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.sm,
    },
    featuredCard: {
      width: 280,
      marginLeft: SPACING.md,
      overflow: "hidden",
    },
    featuredImage: {
      width: "100%",
      height: 160,
      borderRadius: BORDER_RADIUS.md,
      marginBottom: SPACING.sm,
    },
    featuredContent: {
      paddingTop: 0,
    },
    featuredTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 20,
    },
    featuredCategory: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    articleCard: {
      marginHorizontal: SPACING.md,
      marginVertical: SPACING.xs,
    },
    articleRow: {
      flexDirection: "row",
    },
    articleImage: {
      width: 100,
      height: 80,
      borderRadius: BORDER_RADIUS.md,
      marginRight: SPACING.md,
    },
    articleContent: {
      flex: 1,
    },
    articleTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 20,
    },
    articleSummary: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
      lineHeight: 18,
    },
    articleMeta: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    articleCategory: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: "500",
    },
    articleTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
  });

export default HomeScreen;
