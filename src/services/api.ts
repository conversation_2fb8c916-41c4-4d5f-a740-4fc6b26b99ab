import axios, { AxiosInstance, AxiosError } from "axios";
import {
  DataSourceItem,
  NewsCategory,
  ApiResponse,
  PaginatedResponse,
} from "../types";
import { NEWS_CATEGORIES } from "../constants";
import { cacheManager } from "../utils/cache";
import {
  getCurrentApiConfig,
  API_ENDPOINTS,
  CACHE_CONFIG,
  REQUEST_CONFIG,
} from "../config/api";
import {
  transformToNewsArticle,
  transformToFlashNews,
  transformToTweet,
} from "../utils/dataTransform";
import {
  withRetry,
  handleError,
  ErrorType,
  isValidPaginatedResponse,
} from "../utils/errorHandler";

class ApiService {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor() {
    const config = getCurrentApiConfig();
    this.baseUrl = config.baseURL;

    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: config.timeout,
      headers: REQUEST_CONFIG.headers,
    });

    // 设置请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log(
          `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`
        );
        return config;
      },
      (error) => {
        console.error("❌ API Request Error:", error);
        return Promise.reject(error);
      }
    );

    // 设置响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(
          `✅ API Response: ${response.status} ${response.config.url}`
        );
        return response;
      },
      (error: AxiosError) => {
        console.error("❌ API Response Error:", error.message);

        // 处理网络错误
        if (!error.response) {
          throw new Error("网络连接失败，请检查网络设置");
        }

        // 处理HTTP错误状态码
        const status = error.response.status;
        switch (status) {
          case 400:
            throw new Error("请求参数错误");
          case 401:
            throw new Error("未授权访问");
          case 403:
            throw new Error("访问被拒绝");
          case 404:
            throw new Error("请求的资源不存在");
          case 500:
            throw new Error("服务器内部错误");
          case 503:
            throw new Error("服务暂时不可用");
          default:
            throw new Error(`请求失败 (${status})`);
        }
      }
    );
  }

  // 数据转换方法：将后端数据转换为前端期望的格式
  private transformDataSourceItem = (item: any): DataSourceItem => {
    return transformToNewsArticle(item);
  };

  // 转换分类数据
  private transformCategory = (category: any): NewsCategory => {
    if (typeof category === "string") {
      // 如果后端返回的是字符串，查找对应的分类对象
      const foundCategory = NEWS_CATEGORIES.find(
        (cat) => cat.slug === category
      );
      return foundCategory || NEWS_CATEGORIES[0]; // 默认返回第一个分类
    }

    // 如果后端返回的是对象，直接使用
    return {
      id: category.id || category.slug || "general",
      name: category.name || "综合",
      slug: category.slug || "general",
      color: category.color || "#007AFF",
      icon: category.icon || "newspaper",
    };
  };

  // 获取文章列表
  async getArticles(
    page: number = 1,
    limit: number = 20,
    category?: string,
    dataTypes?: string[],
    hasCover?: boolean,
    titleKeywords?: string[],
    sortBy?: string,
    sortOrder?: "asc" | "desc"
  ): Promise<ApiResponse<PaginatedResponse<DataSourceItem>>> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append("page", page.toString());
      queryParams.append("limit", limit.toString());
      if (category && category !== "all") {
        queryParams.append("category", category);
      }
      if (dataTypes && dataTypes.length > 0) {
        queryParams.append("dataTypes", dataTypes.join(","));
      }
      if (hasCover !== undefined) {
        queryParams.append("hasCover", hasCover.toString());
      }
      if (titleKeywords && titleKeywords.length > 0) {
        queryParams.append("titleKeywords", titleKeywords.join(","));
      }
      if (sortBy) {
        queryParams.append("sortBy", sortBy);
      }
      if (sortOrder) {
        queryParams.append("sortOrder", sortOrder);
      }

      // 检查缓存
      const cacheKey = `articles_${queryParams.toString()}`;
      const cachedData = await cacheManager.get<
        ApiResponse<PaginatedResponse<DataSourceItem>>
      >(cacheKey);

      if (cachedData) {
        console.log("📦 Using cached articles data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(
        `${API_ENDPOINTS.ARTICLE}?${queryParams.toString()}`
      );

      // 处理后端响应格式（可能是直接数组或包含分页信息的对象）
      let items: any[] = [];
      let pagination: any = {};

      if (response.data.data.items) {
        // 新格式：包含分页信息
        items = response.data.data.items;
        pagination = response.data.data.pagination || {};
      } else if (Array.isArray(response.data.data)) {
        // 旧格式：直接数组
        items = response.data.data;
        pagination = {
          page,
          limit,
          total: items.length,
          hasMore: items.length >= limit,
        };
      }

      // 转换数据格式以确保兼容性
      const transformedData: PaginatedResponse<DataSourceItem> = {
        items: items.map(this.transformNewsArticle),
        total: pagination.total || items.length,
        page: pagination.page || page,
        limit: pagination.limit || limit,
        hasMore:
          pagination.hasNext || pagination.hasMore || items.length >= limit,
        totalPages:
          pagination.pages ||
          Math.ceil((pagination.total || items.length) / limit),
      };

      const result = {
        success: true,
        data: transformedData,
        message: response.data.message || "获取文章列表成功",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.ARTICLES);

      return result;
    } catch (error) {
      console.error("获取文章列表失败:", error);

      // 尝试从缓存获取
      const cacheKey = `articles_${page}_${limit}_${category || "all"}`;
      const cachedData = await cacheManager.get<
        ApiResponse<PaginatedResponse<DataSourceItem>>
      >(cacheKey);

      if (cachedData) {
        console.log("📦 Using cached articles data (fallback)");
        return cachedData;
      }

      throw new Error(
        error instanceof Error ? error.message : "获取文章列表失败"
      );
    }
  }

  // 获取单篇文章详情
  async getArticleById(id: string): Promise<ApiResponse<DataSourceItem>> {
    try {
      // 检查缓存
      const cacheKey = `article_${id}`;
      const cachedData = await cacheManager.get<ApiResponse<DataSourceItem>>(
        cacheKey
      );

      if (cachedData) {
        console.log("📦 Using cached article data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(API_ENDPOINTS.NEWS_DETAIL(id));

      // 转换数据格式
      const transformedArticle = this.transformNewsArticle(response.data.data);

      const result = {
        success: true,
        data: transformedArticle,
        message: response.data.message || "获取文章详情成功",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.ARTICLE_DETAIL);

      return result;
    } catch (error) {
      console.error("获取文章详情失败:", error);

      // 尝试从缓存获取
      const cacheKey = `article_${id}`;
      const cachedData = await cacheManager.get<ApiResponse<DataSourceItem>>(
        cacheKey
      );

      if (cachedData) {
        console.log("📦 Using cached article data (fallback)");
        return cachedData;
      }

      throw new Error(
        error instanceof Error ? error.message : "获取文章详情失败"
      );
    }
  }

  // 搜索文章
  async searchArticles(
    query: string,
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedResponse<DataSourceItem>>> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append("q", query);
      queryParams.append("page", page.toString());
      queryParams.append("limit", limit.toString());

      // 检查缓存
      const cacheKey = `search_${queryParams.toString()}`;
      const cachedData = await cacheManager.get<
        ApiResponse<PaginatedResponse<DataSourceItem>>
      >(cacheKey);

      if (cachedData) {
        console.log("📦 Using cached search data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(
        `${API_ENDPOINTS.SEARCH}?${queryParams.toString()}`
      );

      // 处理搜索响应格式
      let items: any[] = [];
      let pagination: any = {};

      if (response.data.data.items) {
        items = response.data.data.items;
        pagination = response.data.data.pagination || {};
      } else if (Array.isArray(response.data.data)) {
        items = response.data.data;
        pagination = {
          page,
          limit,
          total: items.length,
          hasMore: items.length >= limit,
        };
      }

      // 转换数据格式
      const transformedData: PaginatedResponse<DataSourceItem> = {
        items: items.map(this.transformNewsArticle),
        total: pagination.total || items.length,
        page: pagination.page || page,
        limit: pagination.limit || limit,
        hasMore:
          pagination.hasNext || pagination.hasMore || items.length >= limit,
        totalPages:
          pagination.pages ||
          Math.ceil((pagination.total || items.length) / limit),
      };

      const result = {
        success: true,
        data: transformedData,
        message: response.data.message || "搜索完成",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.SEARCH);

      return result;
    } catch (error) {
      console.error("搜索失败:", error);

      // 尝试从缓存获取
      const cacheKey = `search_${query}_${page}_${limit}`;
      const cachedData = await cacheManager.get<
        ApiResponse<PaginatedResponse<DataSourceItem>>
      >(cacheKey);

      if (cachedData) {
        console.log("📦 Using cached search data (fallback)");
        return cachedData;
      }

      throw new Error(error instanceof Error ? error.message : "搜索失败");
    }
  }

  // 获取热门文章
  async getTrendingArticles(
    limit: number = 10
  ): Promise<ApiResponse<DataSourceItem[]>> {
    try {
      // 检查缓存
      const cacheKey = `trending_${limit}`;
      const cachedData = await cacheManager.get<ApiResponse<DataSourceItem[]>>(
        cacheKey
      );

      if (cachedData) {
        console.log("📦 Using cached trending data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(
        `${API_ENDPOINTS.NEWS_TRENDING}?limit=${limit}`
      );

      // 转换数据格式
      const transformedArticles = response.data.data.map(
        this.transformNewsArticle
      );

      const result = {
        success: true,
        data: transformedArticles,
        message: response.data.message || "获取热门文章成功",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.TRENDING);

      return result;
    } catch (error) {
      console.error("获取热门文章失败:", error);

      throw new Error(
        error instanceof Error ? error.message : "获取热门文章失败"
      );
    }
  }

  transformNewsArticle(data: any) {
    return data;
  }

  // 获取快讯列表
  async getFlashNews(
    page: number = 1,
    limit: number = 20,
    priority?: string,
    hours?: number
  ): Promise<ApiResponse<PaginatedResponse<DataSourceItem>>> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append("page", page.toString());
      queryParams.append("limit", limit.toString());
      if (priority) queryParams.append("priority", priority);

      // 检查缓存
      const cacheKey = `flash_${queryParams.toString()}`;
      const cachedData = await cacheManager.get<
        ApiResponse<PaginatedResponse<DataSourceItem>>
      >(cacheKey);

      if (cachedData) {
        console.log("📦 Using cached flash news data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(
        `${API_ENDPOINTS.FLASH}?${queryParams.toString()}`
      );

      // 处理响应格式
      let items: any[] = [];
      let pagination: any = {};

      if (response.data.data.items) {
        items = response.data.data.items;
        pagination = response.data.data.pagination || {};
      } else if (Array.isArray(response.data.data)) {
        items = response.data.data;
        pagination = {
          page,
          limit,
          total: items.length,
          hasMore: items.length >= limit,
        };
      }

      // 转换数据格式
      const transformedData: PaginatedResponse<DataSourceItem> = {
        items: items.map(transformToFlashNews),
        total: pagination.total || items.length,
        page: pagination.page || page,
        limit: pagination.limit || limit,
        hasMore:
          pagination.hasNext || pagination.hasMore || items.length >= limit,
        totalPages:
          pagination.pages ||
          Math.ceil((pagination.total || items.length) / limit),
      };

      const result = {
        success: true,
        data: transformedData,
        message: response.data.message || "获取快讯成功",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.FLASH);

      return result;
    } catch (error) {
      console.error("获取快讯失败:", error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (
        __DEV__ &&
        error instanceof Error &&
        error.message.includes("网络连接失败")
      ) {
        console.log("🔄 Backend not available, using mock flash news");
        return {
          success: true,
          data: { items: [], total: 0, page, limit, hasMore: false },
          message: "使用模拟数据（后端服务不可用）",
        };
      }

      throw new Error(error instanceof Error ? error.message : "获取快讯失败");
    }
  }

  // 获取价格数据
  async getPrices(): Promise<ApiResponse<any[]>> {
    try {
      // 检查缓存
      const cacheKey = "prices";
      const cachedData = await cacheManager.get<ApiResponse<any[]>>(cacheKey);

      if (cachedData) {
        console.log("📦 Using cached prices data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(API_ENDPOINTS.PRICES);

      const result = {
        success: true,
        data: response.data.data,
        message: response.data.message || "获取价格数据成功",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.PRICES);

      return result;
    } catch (error) {
      console.error("获取价格数据失败:", error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (
        __DEV__ &&
        error instanceof Error &&
        error.message.includes("网络连接失败")
      ) {
        console.log("🔄 Backend not available, using mock prices");
        return {
          success: true,
          data: [],
          message: "使用模拟数据（后端服务不可用）",
        };
      }

      throw new Error(
        error instanceof Error ? error.message : "获取价格数据失败"
      );
    }
  }

  // 获取热门推特
  async getHotTweets(
    limit: number = 10
  ): Promise<ApiResponse<DataSourceItem[]>> {
    try {
      // 检查缓存
      const cacheKey = `hot_tweets_${limit}`;
      const cachedData = await cacheManager.get<ApiResponse<DataSourceItem[]>>(
        cacheKey
      );

      if (cachedData) {
        console.log("📦 Using cached hot tweets data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(
        `${API_ENDPOINTS.HOT_TWEETS}?limit=${limit}`
      );

      console.log("response:", response);
      // 转换数据格式
      let items = response.data.data.items || [];
      const transformedTweets = Array.isArray(items)
        ? items.map(transformToTweet)
        : [];

      const result = {
        success: true,
        data: transformedTweets,
        message: response.data.message || "获取热门推特成功",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.TRENDING);

      return result;
    } catch (error) {
      console.error("获取热门推特失败:", error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (
        __DEV__ &&
        error instanceof Error &&
        error.message.includes("网络连接失败")
      ) {
        console.log("🔄 Backend not available, using mock tweets");
        return {
          success: true,
          data: [],
          message: "使用模拟数据（后端服务不可用）",
        };
      }

      throw new Error(
        error instanceof Error ? error.message : "获取热门推特失败"
      );
    }
  }

  // 获取精选文章
  async getFeaturedArticles(
    limit: number = 5
  ): Promise<ApiResponse<DataSourceItem[]>> {
    try {
      const response = await this.getArticles(
        1,
        limit,
        undefined,
        ["article"],
        true, // 必须有封面
        ["精选"], // 标题包含"精选"
        "publishTime",
        "desc"
      );

      if (response.success) {
        return {
          success: true,
          data: response.data.items,
          message: "获取精选文章成功",
        };
      }

      throw new Error(response.message || "获取精选文章失败");
    } catch (error) {
      console.error("获取精选文章失败:", error);
      throw new Error(
        error instanceof Error ? error.message : "获取精选文章失败"
      );
    }
  }

  // 获取热门资讯
  async getHotNews(limit: number = 20): Promise<ApiResponse<DataSourceItem[]>> {
    try {
      const response = await this.getFlashNews(1, limit);

      if (response.success) {
        return {
          success: true,
          data: response.data.items,
          message: "获取热门资讯成功",
        };
      }

      throw new Error(response.message || "获取热门资讯失败");
    } catch (error) {
      console.error("获取热门资讯失败:", error);
      throw new Error(
        error instanceof Error ? error.message : "获取热门资讯失败"
      );
    }
  }

  // 获取推特列表
  async getCategories() {
    const result = {
      success: true,
      data: [],
      message: "",
      error: "",
    };
    return result;
  }
  async getTweets(
    page: number = 1,
    limit: number = 20,
    sources?: string
  ): Promise<ApiResponse<PaginatedResponse<DataSourceItem>>> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append("page", page.toString());
      queryParams.append("limit", limit.toString());
      if (sources) queryParams.append("sources", sources);

      // 检查缓存
      const cacheKey = `tweets_${queryParams.toString()}`;
      const cachedData = await cacheManager.get<
        ApiResponse<PaginatedResponse<DataSourceItem>>
      >(cacheKey);

      if (cachedData) {
        console.log("📦 Using cached tweets data");
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(
        `/tweets?${queryParams.toString()}`
      );

      // 处理响应格式
      let items: any[] = [];
      let pagination: any = {};

      if (response?.data?.data?.items) {
        items = response.data.data.items;
        pagination = response.data.data.pagination || {};
      } else if (Array.isArray(response.data.data)) {
        items = response.data.data;
        pagination = {
          page,
          limit,
          total: items.length,
          hasMore: items.length >= limit,
        };
      }

      // 转换数据格式
      const transformedData: PaginatedResponse<DataSourceItem> = {
        items: items.map(transformToTweet),
        total: pagination.total || items.length,
        page: pagination.page || page,
        limit: pagination.limit || limit,
        hasMore:
          pagination.hasNext || pagination.hasMore || items.length >= limit,
        totalPages:
          pagination.pages ||
          Math.ceil((pagination.total || items.length) / limit),
      };

      const result = {
        success: true,
        data: transformedData,
        message: response.data.message || "获取推特列表成功",
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.TRENDING);

      return result;
    } catch (error) {
      console.error("获取推特列表失败:", error);

      throw new Error(
        error instanceof Error ? error.message : "获取推特列表失败"
      );
    }
  }
}

// 创建API服务实例
export const apiService = new ApiService();

// 导出常用方法
export const {
  getArticles,
  getArticleById,
  searchArticles,
  getTrendingArticles,
  getFeaturedArticles,
  getHotNews,
  getFlashNews,
  getPrices,
  getHotTweets,
  getTweets,
  getCategories,
} = apiService;
