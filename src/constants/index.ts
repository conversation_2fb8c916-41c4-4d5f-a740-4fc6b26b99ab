// 应用常量
export const APP_CONFIG = {
  name: "ChainMix",
  version: "1.0.0",
  description: "智能区块链资讯平台",
};

// 主题颜色 - 优化对比度和可访问性
export const COLORS = {
  light: {
    primary: "#007AFF",
    primaryLight: "#4DA2FF",
    primaryDark: "#0056CC",
    secondary: "#5856D6",
    accent: "#FF6B35",
    success: "#34C759",
    warning: "#FF9500",
    error: "#FF3B30",
    background: "#FFFFFF",
    backgroundSecondary: "#F8F9FA",
    surface: "#F2F2F7",
    surfaceElevated: "#FFFFFF",
    text: "#1D1D1F",
    textSecondary: "#6D6D80",
    textTertiary: "#8E8E93",
    border: "#D1D1D6",
    borderLight: "#E5E5EA",
    shadow: "rgba(0, 0, 0, 0.1)",
    overlay: "rgba(0, 0, 0, 0.4)",
  },
  dark: {
    primary: "#0A84FF",
    primaryLight: "#4DA2FF",
    primaryDark: "#0056CC",
    secondary: "#5E5CE6",
    accent: "#FF6B35",
    success: "#30D158",
    warning: "#FF9F0A",
    error: "#FF453A",
    background: "#000000",
    backgroundSecondary: "#0A0A0A",
    surface: "#1C1C1E",
    surfaceElevated: "#2C2C2E",
    text: "#FFFFFF",
    textSecondary: "#EBEBF5",
    textTertiary: "#8E8E93",
    border: "#38383A",
    borderLight: "#48484A",
    shadow: "rgba(0, 0, 0, 0.3)",
    overlay: "rgba(0, 0, 0, 0.6)",
  },
};

// 新闻分类
export const NEWS_CATEGORIES = [
  {
    id: "1",
    name: "DeFi",
    slug: "defi",
    color: "#007AFF",
    icon: "trending-up",
  },
  {
    id: "2",
    name: "NFT",
    slug: "nft",
    color: "#5856D6",
    icon: "image",
  },
  {
    id: "3",
    name: "加密货币",
    slug: "cryptocurrency",
    color: "#FF9500",
    icon: "logo-bitcoin",
  },
  {
    id: "4",
    name: "区块链技术",
    slug: "blockchain-tech",
    color: "#34C759",
    icon: "cube",
  },
  {
    id: "5",
    name: "监管政策",
    slug: "regulation",
    color: "#FF3B30",
    icon: "shield",
  },
  {
    id: "6",
    name: "市场分析",
    slug: "market-analysis",
    color: "#AF52DE",
    icon: "analytics",
  },
];

// API 配置
export const API_CONFIG = {
  baseUrl: "https://api.blockchainnews.com",
  timeout: 10000,
  retryAttempts: 3,
};

// 分页配置
export const PAGINATION = {
  defaultLimit: 20,
  maxLimit: 100,
};

// 缓存配置
export const CACHE_CONFIG = {
  articlesCacheDuration: 1 * 60 * 1000, // 1分钟
  categoriesCacheDuration: 1 * 60 * 1000, // 1分钟
};

// 动画配置
export const ANIMATION_CONFIG = {
  duration: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  easing: {
    easeInOut: "ease-in-out",
    easeOut: "ease-out",
    spring: "spring",
  },
};

// 字体大小 - 基于iOS Human Interface Guidelines
export const FONT_SIZES = {
  xs: 11, // Caption 2
  sm: 12, // Caption 1
  md: 14, // Footnote
  base: 16, // Body (默认)
  lg: 17, // Callout
  xl: 20, // Title 3
  "2xl": 22, // Title 2
  "3xl": 28, // Title 1
  "4xl": 34, // Large Title
  "5xl": 40, // Display
};

// 字体权重
export const FONT_WEIGHTS = {
  light: "300",
  normal: "400",
  medium: "500",
  semibold: "600",
  bold: "700",
  heavy: "800",
  black: "900",
} as const;

// 行高
export const LINE_HEIGHTS = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
};

// 间距系统 - 8pt网格系统
export const SPACING = {
  xs: 4, // 0.5 * 8
  sm: 8, // 1 * 8
  md: 16, // 2 * 8
  lg: 24, // 3 * 8
  xl: 32, // 4 * 8
  "2xl": 40, // 5 * 8
  "3xl": 48, // 6 * 8
  "4xl": 64, // 8 * 8
  "5xl": 80, // 10 * 8
};

// 圆角
export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  "2xl": 20,
  "3xl": 24,
  full: 9999,
};

// 阴影
export const SHADOWS = {
  sm: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  xl: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
};

export const SPRING_CONFIG = {
  tension: 100,
  friction: 10,
};
export const SPLASH_TIME = 2500
// 统一的布局边距规范
export const LAYOUT = {
  // 页面水平边距 - 统一使用SPACING.md (16px)
  pageHorizontalPadding: SPACING.md,

  // 内容区域边距
  contentPadding: SPACING.md,

  // 卡片内边距
  cardPadding: SPACING.md,

  // 列表项边距
  listItemPadding: SPACING.md,

  // 模块间距
  sectionSpacing: SPACING.lg,

  // 组件间距
  componentSpacing: SPACING.md,
};
