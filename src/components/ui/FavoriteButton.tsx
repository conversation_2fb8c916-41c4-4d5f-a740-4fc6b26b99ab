import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { useAppStore } from '../../store';
import { SPACING } from '../../constants';
import TouchableScale from './TouchableScale';

interface FavoriteButtonProps {
  articleId: string;
  size?: number;
  style?: ViewStyle;
}

const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  articleId,
  size = 24,
  style,
}) => {
  const { colors } = useTheme();
  const { favorites, toggleFavorite } = useAppStore();
  
  const isFavorite = favorites.includes(articleId);

  const handlePress = () => {
    toggleFavorite(articleId);
  };

  return (
    <TouchableScale
      onPress={handlePress}
      style={style ? [styles.button, style] : styles.button}
      scaleValue={0.9}
    >
      <Ionicons
        name={isFavorite ? 'heart' : 'heart-outline'}
        size={size}
        color={isFavorite ? colors.error : colors.textSecondary}
      />
    </TouchableScale>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: SPACING.sm,
  },
});

export default FavoriteButton;
