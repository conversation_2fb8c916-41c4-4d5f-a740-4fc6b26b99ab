import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS } from '../../constants';
import { NEWS_CATEGORIES } from '../../constants';
import TouchableScale from './TouchableScale';
import Button from './Button';

const { width: screenWidth } = Dimensions.get('window');

export interface SearchFilters {
  categories: string[];
  timeRange: 'all' | 'today' | 'week' | 'month';
  sortBy: 'relevance' | 'date' | 'popularity';
}

interface SearchFiltersProps {
  visible: boolean;
  onClose: () => void;
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onApply: () => void;
  onReset: () => void;
}

const timeRangeOptions = [
  { key: 'all', label: '全部时间', icon: 'infinite-outline' },
  { key: 'today', label: '今天', icon: 'today-outline' },
  { key: 'week', label: '本周', icon: 'calendar-outline' },
  { key: 'month', label: '本月', icon: 'calendar-outline' },
];

const sortOptions = [
  { key: 'relevance', label: '相关性', icon: 'star-outline' },
  { key: 'date', label: '时间', icon: 'time-outline' },
  { key: 'popularity', label: '热度', icon: 'trending-up-outline' },
];

const SearchFiltersComponent: React.FC<SearchFiltersProps> = ({
  visible,
  onClose,
  filters,
  onFiltersChange,
  onApply,
  onReset,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const toggleCategory = (categoryId: string) => {
    const newCategories = filters.categories.includes(categoryId)
      ? filters.categories.filter(id => id !== categoryId)
      : [...filters.categories, categoryId];
    
    onFiltersChange({
      ...filters,
      categories: newCategories,
    });
  };

  const setTimeRange = (timeRange: SearchFilters['timeRange']) => {
    onFiltersChange({
      ...filters,
      timeRange,
    });
  };

  const setSortBy = (sortBy: SearchFilters['sortBy']) => {
    onFiltersChange({
      ...filters,
      sortBy,
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.categories.length > 0) count++;
    if (filters.timeRange !== 'all') count++;
    if (filters.sortBy !== 'relevance') count++;
    return count;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableScale onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableScale>
          <Text style={styles.title}>搜索筛选</Text>
          <TouchableOpacity onPress={onReset} style={styles.resetButton}>
            <Text style={styles.resetText}>重置</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 分类筛选 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>新闻分类</Text>
            <View style={styles.categoryGrid}>
              {NEWS_CATEGORIES.map((category) => {
                const isSelected = filters.categories.includes(category.id);
                return (
                  <TouchableScale
                    key={category.id}
                    style={[
                      styles.categoryChip,
                      { borderColor: category.color },
                      ...(isSelected ? [styles.categoryChipSelected] : []),
                    ]}
                    onPress={() => toggleCategory(category.id)}
                  >
                    <Ionicons
                      name={category.icon as any}
                      size={16}
                      color={isSelected ? '#FFFFFF' : category.color}
                      style={styles.categoryIcon}
                    />
                    <Text
                      style={[
                        styles.categoryText,
                        isSelected && styles.categoryTextSelected,
                        { color: isSelected ? '#FFFFFF' : category.color },
                      ]}
                    >
                      {category.name}
                    </Text>
                  </TouchableScale>
                );
              })}
            </View>
          </View>

          {/* 时间范围 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>时间范围</Text>
            <View style={styles.optionsList}>
              {timeRangeOptions.map((option) => {
                const isSelected = filters.timeRange === option.key;
                return (
                  <TouchableScale
                    key={option.key}
                    style={isSelected ? [styles.optionItem, styles.optionItemSelected] : styles.optionItem}
                    onPress={() => setTimeRange(option.key as SearchFilters['timeRange'])}
                  >
                    <Ionicons
                      name={option.icon as any}
                      size={20}
                      color={isSelected ? colors.primary : colors.textSecondary}
                      style={styles.optionIcon}
                    />
                    <Text
                      style={[
                        styles.optionText,
                        isSelected && styles.optionTextSelected,
                      ]}
                    >
                      {option.label}
                    </Text>
                    {isSelected && (
                      <Ionicons
                        name="checkmark"
                        size={20}
                        color={colors.primary}
                      />
                    )}
                  </TouchableScale>
                );
              })}
            </View>
          </View>

          {/* 排序方式 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>排序方式</Text>
            <View style={styles.optionsList}>
              {sortOptions.map((option) => {
                const isSelected = filters.sortBy === option.key;
                return (
                  <TouchableScale
                    key={option.key}
                    style={isSelected ? [styles.optionItem, styles.optionItemSelected] : styles.optionItem}
                    onPress={() => setSortBy(option.key as SearchFilters['sortBy'])}
                  >
                    <Ionicons
                      name={option.icon as any}
                      size={20}
                      color={isSelected ? colors.primary : colors.textSecondary}
                      style={styles.optionIcon}
                    />
                    <Text
                      style={[
                        styles.optionText,
                        isSelected && styles.optionTextSelected,
                      ]}
                    >
                      {option.label}
                    </Text>
                    {isSelected && (
                      <Ionicons
                        name="checkmark"
                        size={20}
                        color={colors.primary}
                      />
                    )}
                  </TouchableScale>
                );
              })}
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <Button
            title={`应用筛选${getActiveFiltersCount() > 0 ? ` (${getActiveFiltersCount()})` : ''}`}
            onPress={onApply}
            variant="primary"
            style={styles.applyButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    closeButton: {
      padding: SPACING.sm,
    },
    title: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
    },
    resetButton: {
      padding: SPACING.sm,
    },
    resetText: {
      fontSize: FONT_SIZES.base,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    content: {
      flex: 1,
      paddingHorizontal: SPACING.md,
    },
    section: {
      marginVertical: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.md,
    },
    categoryGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    categoryChip: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      borderWidth: 1,
      backgroundColor: 'transparent',
    },
    categoryChipSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    categoryIcon: {
      marginRight: SPACING.xs,
    },
    categoryText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
    },
    categoryTextSelected: {
      color: '#FFFFFF',
    },
    optionsList: {
      gap: SPACING.xs,
    },
    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    optionItemSelected: {
      backgroundColor: colors.primaryLight + '20',
    },
    optionIcon: {
      marginRight: SPACING.md,
    },
    optionText: {
      flex: 1,
      fontSize: FONT_SIZES.base,
      color: colors.text,
      fontWeight: FONT_WEIGHTS.medium,
    },
    optionTextSelected: {
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.semibold,
    },
    footer: {
      padding: SPACING.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    applyButton: {
      width: '100%',
    },
  });

export default SearchFiltersComponent;
