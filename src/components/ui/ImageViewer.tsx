import React, { useState } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Text,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, FONT_WEIGHTS } from '../../constants';
import TouchableScale from './TouchableScale';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ImageViewerProps {
  visible: boolean;
  imageUrl: string;
  onClose: () => void;
  title?: string;
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  visible,
  imageUrl,
  onClose,
  title,
}) => {
  const { colors } = useTheme();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const styles = createStyles(colors);

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}
      statusBarTranslucent={true}
    >
      <StatusBar backgroundColor="rgba(0,0,0,0.9)" barStyle="light-content" />
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={handleClose}
        >
          <View style={styles.header}>
            <TouchableScale onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={28} color="#FFFFFF" />
            </TouchableScale>
            {title && (
              <Text style={styles.title} numberOfLines={1}>
                {title}
              </Text>
            )}
          </View>

          <ScrollView
            style={styles.imageContainer}
            contentContainerStyle={styles.imageContent}
            maximumZoomScale={3}
            minimumZoomScale={1}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
          >
            {!imageError ? (
              <Image
                source={{ uri: imageUrl }}
                style={styles.image}
                resizeMode="contain"
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
              />
            ) : (
              <View style={styles.errorContainer}>
                <Ionicons
                  name="image-outline"
                  size={64}
                  color="rgba(255,255,255,0.6)"
                />
                <Text style={styles.errorText}>图片加载失败</Text>
              </View>
            )}
          </ScrollView>

          <View style={styles.footer}>
            <Text style={styles.hint}>双指缩放查看图片</Text>
          </View>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingTop: StatusBar.currentHeight || SPACING.xl,
      paddingBottom: SPACING.md,
      zIndex: 1,
    },
    closeButton: {
      padding: SPACING.sm,
      marginRight: SPACING.md,
    },
    title: {
      flex: 1,
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: '#FFFFFF',
    },
    imageContainer: {
      flex: 1,
    },
    imageContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: screenHeight * 0.7,
    },
    image: {
      width: '100%',
      height: '100%',
    },
    errorContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      height: '100%',
    },
    errorText: {
      fontSize: FONT_SIZES.base,
      color: 'rgba(255,255,255,0.6)',
      marginTop: SPACING.md,
    },
    footer: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.lg,
      alignItems: 'center',
    },
    hint: {
      fontSize: FONT_SIZES.sm,
      color: 'rgba(255,255,255,0.7)',
      textAlign: 'center',
    },
  });

export default ImageViewer;
