import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { useTheme } from '../common/ThemeProvider';
import { SPACING } from '../../constants';

const { width: screenWidth } = Dimensions.get('window');

interface ReadingProgressBarProps {
  progress: number; // 0-1 之间的值
  style?: any;
}

const ReadingProgressBar: React.FC<ReadingProgressBarProps> = ({
  progress,
  style,
}) => {
  const { colors } = useTheme();
  const progressAnim = useRef(new Animated.Value(0)).current;
  const styles = createStyles(colors);

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [progress]);

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });

  return (
    <View style={[styles.container, style]}>
      <View style={styles.track}>
        <Animated.View
          style={[
            styles.progress,
            {
              width: progressWidth,
            },
          ]}
        />
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      width: '100%',
      paddingHorizontal: SPACING.md,
    },
    track: {
      height: 3,
      backgroundColor: colors.border,
      borderRadius: 1.5,
      overflow: 'hidden',
    },
    progress: {
      height: '100%',
      backgroundColor: colors.primary,
      borderRadius: 1.5,
    },
  });

export default ReadingProgressBar;
