import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import { useAppStore } from '../../store';

interface ImagePlaceholderProps {
  width: number;
  height: number;
  style?: ViewStyle;
  iconName?: keyof typeof Ionicons.glyphMap;
  text?: string;
}

const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({
  width,
  height,
  style,
  iconName = 'image-outline',
  text = '图片加载中...'
}) => {
  const { theme } = useAppStore();
  const colors = COLORS[theme];

  const styles = createStyles(colors, width, height);

  return (
    <View style={[styles.container, style]}>
      <Ionicons 
        name={iconName} 
        size={Math.min(width, height) * 0.3} 
        color={colors.textTertiary} 
      />
      <Text style={styles.text}>{text}</Text>
    </View>
  );
};

const createStyles = (colors: typeof COLORS.light, width: number, height: number) =>
  StyleSheet.create({
    container: {
      width,
      height,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.borderLight,
    },
    text: {
      fontSize: FONT_SIZES.xs,
      color: colors.textTertiary,
      marginTop: 4,
      textAlign: 'center',
    },
  });

export default ImagePlaceholder;
