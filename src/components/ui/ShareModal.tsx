import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Share,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS } from '../../constants';
import TouchableScale from './TouchableScale';

const { width: screenWidth } = Dimensions.get('window');

interface ShareOption {
  id: string;
  title: string;
  icon: string;
  color: string;
  action: () => void;
}

interface ShareModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  url: string;
  content: string;
}

const ShareModal: React.FC<ShareModalProps> = ({
  visible,
  onClose,
  title,
  url,
  content,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const handleNativeShare = async () => {
    try {
      await Share.share({
        message: `${title}\n\n${content}\n\n${url}`,
        url: url,
        title: title,
      });
      onClose();
    } catch (error) {
      Alert.alert('分享失败', '无法分享此内容');
    }
  };

  const handleCopyLink = async () => {
    // 这里可以使用 Clipboard API
    Alert.alert('链接已复制', '文章链接已复制到剪贴板');
    onClose();
  };

  const shareOptions: ShareOption[] = [
    {
      id: 'native',
      title: '系统分享',
      icon: 'share-outline',
      color: colors.primary,
      action: handleNativeShare,
    },
    {
      id: 'copy',
      title: '复制链接',
      icon: 'copy-outline',
      color: colors.secondary,
      action: handleCopyLink,
    },
    {
      id: 'wechat',
      title: '微信',
      icon: 'chatbubble-outline',
      color: '#07C160',
      action: () => {
        Alert.alert('功能开发中', '微信分享功能正在开发中');
        onClose();
      },
    },
    {
      id: 'weibo',
      title: '微博',
      icon: 'logo-twitter',
      color: '#E6162D',
      action: () => {
        Alert.alert('功能开发中', '微博分享功能正在开发中');
        onClose();
      },
    },
  ];

  const renderShareOption = (option: ShareOption) => (
    <TouchableScale
      key={option.id}
      style={styles.shareOption}
      onPress={option.action}
    >
      <View style={[styles.shareIconContainer, { backgroundColor: option.color + '20' }]}>
        <Ionicons
          name={option.icon as any}
          size={24}
          color={option.color}
        />
      </View>
      <Text style={styles.shareOptionText}>{option.title}</Text>
    </TouchableScale>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>分享文章</Text>
            <TouchableScale onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={colors.textSecondary} />
            </TouchableScale>
          </View>

          <View style={styles.articlePreview}>
            <Text style={styles.articleTitle} numberOfLines={2}>
              {title}
            </Text>
            <Text style={styles.articleContent} numberOfLines={3}>
              {content}
            </Text>
          </View>

          <View style={styles.optionsContainer}>
            {shareOptions.map(renderShareOption)}
          </View>

          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelText}>取消</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: colors.overlay,
      justifyContent: 'flex-end',
    },
    container: {
      backgroundColor: colors.surfaceElevated,
      borderTopLeftRadius: BORDER_RADIUS.xl,
      borderTopRightRadius: BORDER_RADIUS.xl,
      paddingBottom: SPACING.xl,
      maxHeight: '70%',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    title: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
    },
    closeButton: {
      padding: SPACING.xs,
    },
    articlePreview: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    articleTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.sm,
      lineHeight: 20,
    },
    articleContent: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
    },
    optionsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.lg,
      justifyContent: 'space-around',
    },
    shareOption: {
      alignItems: 'center',
      marginBottom: SPACING.md,
      width: (screenWidth - SPACING.md * 2) / 4,
    },
    shareIconContainer: {
      width: 56,
      height: 56,
      borderRadius: 28,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    shareOptionText: {
      fontSize: FONT_SIZES.sm,
      color: colors.text,
      fontWeight: FONT_WEIGHTS.medium,
      textAlign: 'center',
    },
    cancelButton: {
      marginHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      alignItems: 'center',
    },
    cancelText: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
  });

export default ShareModal;
