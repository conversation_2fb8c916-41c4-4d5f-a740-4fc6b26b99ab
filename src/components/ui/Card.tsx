import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, BORDER_RADIUS, SHADOWS } from '../../constants';

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: keyof typeof SPACING;
  style?: ViewStyle;
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  onPress,
  variant = 'default',
  padding = 'md',
  style,
  disabled = false,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors, variant, padding);

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[styles.card, style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={onPress ? 0.8 : 1}
    >
      {children}
    </CardComponent>
  );
};

const createStyles = (
  colors: any,
  variant: CardProps['variant'],
  padding: keyof typeof SPACING
) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING[padding],
    };

    switch (variant) {
      case 'default':
        baseStyle.backgroundColor = colors.surface;
        break;
      case 'elevated':
        baseStyle.backgroundColor = colors.surfaceElevated;
        baseStyle.shadowColor = colors.shadow;
        Object.assign(baseStyle, SHADOWS.md);
        break;
      case 'outlined':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = colors.border;
        break;
    }

    return baseStyle;
  };

  return StyleSheet.create({
    card: getCardStyle(),
  });
};

export default Card;
