import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { COLORS } from '../../constants';

type ThemeContextType = {
  theme: 'light' | 'dark';
  colors: typeof COLORS.light;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // 使用本地状态避免循环依赖
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  // 延迟加载 store 以避免循环依赖
  useEffect(() => {
    const loadThemeFromStore = async () => {
      try {
        // 动态导入 store 以避免循环依赖
        const { useAppStore } = await import('../../store');
        const storeTheme = useAppStore.getState().theme;
        setTheme(storeTheme);

        // 订阅 store 变化
        const unsubscribe = useAppStore.subscribe((state) => {
          setTheme(state.theme);
        });

        return unsubscribe;
      } catch (error) {
        console.warn('Failed to load theme from store, using default:', error);
      }
    };

    loadThemeFromStore();
  }, []);

  const toggleTheme = async () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);

    try {
      // 动态导入 store 以避免循环依赖
      const { useAppStore } = await import('../../store');
      useAppStore.getState().setTheme(newTheme);
    } catch (error) {
      console.warn('Failed to save theme to store:', error);
    }
  };

  const colors = COLORS[theme];

  const value: ThemeContextType = {
    theme,
    colors,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    // 在开发环境中提供更详细的错误信息
    if (__DEV__) {
      console.warn('useTheme hook called outside of ThemeProvider. This might indicate a circular dependency or incorrect component structure.');
      console.warn('Stack trace:', new Error().stack);
    }

    // 提供一个默认的主题作为后备，避免应用崩溃
    return {
      theme: 'light',
      colors: COLORS.light,
      toggleTheme: () => {
        if (__DEV__) {
          console.warn('toggleTheme called outside ThemeProvider - this action will be ignored');
        }
      },
    };
  }
  return context;
};
