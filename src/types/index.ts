// 通用数据项类型（对应后端 DataSourceItem）
// 这是前端的主要数据类型，所有内容都使用这个类型
export interface DataSourceItem {
  id: string;
  title: string;
  content?: string;
  description?: string;
  url?: string;
  publishTime: string;
  source: string;
  dataType: string;
  tags?: string[];
  author?: string;
  originalData?: any;
  metadata?: {
    importance?: number;
    readCount?: number;
    likeCount?: number;
    shareCount?: number;
    replyCount?: number;
    hotScore?: number;
    followerCount?: number;
    amount?: string;
    round?: string;
    investors?: string;
    field?: string;
    [key: string]: any;
  };
  cover?: string;
  TweetUrl?: string;
  tweetText?: string;
  sourceUrl?: string;
  // 前端特有字段
  isFavorite?: boolean;
  readTime?: number; // 阅读时间（分钟）
  category?: NewsCategory;
  engagement?: {
    views?: number;
    likes?: number;
    shares?: number;
    replies?: number;
  };
  summary?: string;
  articleContent?: string;
}

export interface NewsCategory {
  id: string;
  name: string;
  slug: string;
  color: string;
  icon: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: "light" | "dark" | "system";
  language: string;
  notifications: boolean;
  favoriteCategories: string[];
}

// 导航类型
export type RootStackParamList = {
  Main: undefined;
  ArticleDetail: { articleId: string };
  FeaturedList: undefined;
  TwitterList: undefined;
  TrendingList: undefined;
  APITest: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  News: undefined;
  AI: undefined;
  Discover: undefined;
  Profile: undefined;
};

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp?: string; // 后端额外字段
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  totalPages?: number; // 后端额外字段
}

// 主题类型
export type ThemeMode = "light" | "dark";

// 状态类型
export interface AppState {
  theme: ThemeMode;
  isLoading: boolean;
  user: User | null;
  favorites: string[];
}

export interface NewsState {
  articles: DataSourceItem[];
  categories: NewsCategory[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  hasMore: boolean;
}

// 组件Props类型
export interface BaseComponentProps {
  style?: any;
  testID?: string;
}

export interface TouchableComponentProps extends BaseComponentProps {
  onPress?: () => void;
  disabled?: boolean;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// 加载状态类型
export type LoadingState = "idle" | "loading" | "success" | "error";

// 搜索相关类型
export interface SearchState {
  query: string;
  results: DataSourceItem[];
  isLoading: boolean;
  error: string | null;
}

// API 响应的分页元数据
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 扩展的 API 响应类型
export interface ExtendedApiResponse<T> {
  success: boolean;
  data:
    | {
        items?: T[];
        pagination?: PaginationMeta;
        metadata?: {
          [key: string]: any;
        };
      }
    | T;
  message?: string;
  error?: string;
  timestamp?: string;
}

// 类型别名，为了向后兼容和语义清晰
export type FlashNewsItem = DataSourceItem;
export type TweetItem = DataSourceItem;
export type NewsArticle = DataSourceItem;

// 更多类型别名，为了向后兼容和语义清晰
export type FinanceItem = DataSourceItem;
export type PriceItem = DataSourceItem;

export type ToolEventItem = DataSourceItem;

// 统一的内容项类型（现在都是 DataSourceItem）
export type ContentItem = DataSourceItem;

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// 性能指标
export interface PerformanceMetrics {
  renderTime: number;
  loadTime: number;
  memoryUsage?: number;
  cacheHitRate?: number;
}

// 搜索过滤器
export interface SearchFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  sources?: string[];
  dataTypes?: string[];
  tags?: string[];
  sortBy?: "publishTime" | "relevance" | "popularity";
  sortOrder?: "asc" | "desc";
}

// 高级搜索选项
export interface AdvancedSearchOptions extends SearchFilters {
  includeContent?: boolean;
  exactMatch?: boolean;
  excludeWords?: string[];
  minReadTime?: number;
  maxReadTime?: number;
}

// 数据源配置
export interface DataSourceConfig {
  id: string;
  name: string;
  url: string;
  enabled: boolean;
  priority: number;
  rateLimit: number;
  timeout: number;
  retryCount: number;
}

// 缓存配置
export interface CacheConfig {
  ttl: number;
  maxSize: number;
  strategy: "lru" | "fifo" | "ttl";
}

// API 配置
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retryCount: number;
  retryDelay: number;
  cache: CacheConfig;
}

// 组件属性基类
export interface BaseComponentProps {
  testID?: string;
  style?: any;
  onError?: (error: AppError) => void;
}

// 列表组件属性
export interface ListComponentProps<T> extends BaseComponentProps {
  data: T[];
  loading?: boolean;
  error?: AppError | null;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  renderItem: (item: T, index: number) => React.ReactElement;
  keyExtractor?: (item: T, index: number) => string;
}

// 搜索组件属性
export interface SearchComponentProps extends BaseComponentProps {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onSubmit?: (text: string) => void;
  filters?: SearchFilters;
  onFiltersChange?: (filters: SearchFilters) => void;
}

// 卡片组件属性
export interface CardComponentProps extends BaseComponentProps {
  title: string;
  subtitle?: string;
  content?: string;
  imageUrl?: string;
  onPress?: () => void;
  actions?: Array<{
    label: string;
    onPress: () => void;
    icon?: string;
  }>;
}
