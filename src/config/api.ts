/**
 * API 配置文件
 * 管理不同环境下的API地址和配置
 */

// API 环境配置
export const API_CONFIG = {
  development: {
    baseURL: 'http://localhost:3001/api',
    timeout: 10000,
  },
  production: {
    baseURL: 'https://api.chainmix.com/api',
    timeout: 15000,
  },
};

// 获取当前环境配置
export const getCurrentApiConfig = () => {
  return __DEV__ ? API_CONFIG.development : API_CONFIG.production;
};

// API 端点配置
export const API_ENDPOINTS = {
  // 新闻相关
  NEWS: '/news',
  ARTICLE: '/article',
  NEWS_DETAIL: (id: string) => `/news/${id}`,
  NEWS_TRENDING: '/news/trending',
  NEWS_RECOMMENDED: (id: string) => `/news/${id}/recommended`,
  NEWS_FEATURED: '/news/featured',
  NEWS_BY_SOURCE: (source: string) => `/news/by-source/${source}`,

  // 快讯相关
  FLASH: '/flash',
  FLASH_SEARCH: '/flash/search',

  // 搜索相关 - 修正路径
  SEARCH: '/news/search',

  // 分类相关 - 修正路径
  CATEGORIES: '/news/categories',

  // 价格相关
  PRICES: '/prices',
  PRICES_TRENDING: '/prices/trending',
  PRICES_TOP: '/prices/top',
  PRICES_MARKET_OVERVIEW: '/prices/market-overview',
  PRICES_SYMBOL: (symbol: string) => `/prices/symbol/${symbol}`,
  PRICES_HISTORY: (symbol: string) => `/prices/symbol/${symbol}/history`,

  // 推特相关 - 需要后端实现
  HOT_TWEETS: '/tweets/hot',
  TWEETS_BY_SOURCE: (source: string) => `/tweets/by-source/${source}`,

  // 统计相关
  STATS: '/stats',

  // 数据源相关
  SOURCES: '/sources',

  // 融资相关
  FINANCING: '/financing',
  FINANCING_TRENDING: '/financing/trending',
};

// 缓存配置
export const CACHE_CONFIG = {
  // 文章列表缓存时间（1分钟）
  ARTICLES: 1 * 60 * 1000,
  
  // 文章详情缓存时间（1分钟）
  ARTICLE_DETAIL: 1 * 60 * 1000,
  
  // 分类列表缓存时间（1分钟）
  CATEGORIES: 1 * 60 * 1000,
  
  // 搜索结果缓存时间（1分钟）
  SEARCH: 1 * 60 * 1000,
  
  // 热门文章缓存时间（1分钟）
  TRENDING: 1 * 60 * 1000,
  
  // 推荐文章缓存时间（1分钟）
  RECOMMENDED: 1 * 60 * 1000,
  
  // 快讯缓存时间（1分钟）
  FLASH: 1 * 60 * 1000,
  
  // 价格数据缓存时间（1分钟）
  PRICES: 1 * 60 * 1000,
};

// 请求配置
export const REQUEST_CONFIG = {
  // 默认请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // 重试配置
  retry: {
    times: 3,
    delay: 1000,
  },
};
