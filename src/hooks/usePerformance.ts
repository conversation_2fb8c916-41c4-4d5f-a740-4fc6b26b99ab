import { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { PerformanceMonitor, debounce, throttle } from '../utils/performance';

/**
 * 性能优化相关的 React Hooks
 */

// 性能监控 Hook
export const usePerformanceMonitor = (componentName: string) => {
  const monitor = useMemo(() => PerformanceMonitor.getInstance(), []);
  const renderStartTime = useRef<number>(0);

  useEffect(() => {
    renderStartTime.current = performance.now();
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    monitor.recordMetric(`${componentName}_render`, renderTime);
  });

  const startTiming = useCallback((label: string) => {
    return monitor.startTiming(`${componentName}_${label}`);
  }, [monitor, componentName]);

  const getMetrics = useCallback((label?: string) => {
    const fullLabel = label ? `${componentName}_${label}` : componentName;
    return monitor.getMetrics(fullLabel);
  }, [monitor, componentName]);

  return { startTiming, getMetrics, monitor };
};

// 防抖 Hook
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T => {
  return useMemo(
    () => debounce(callback, delay),
    [delay, ...deps]
  ) as T;
};

// 节流 Hook
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  limit: number,
  deps: React.DependencyList = []
): T => {
  return useMemo(
    () => throttle(callback, limit),
    [limit, ...deps]
  ) as T;
};

// 内存化 Hook
export const useMemoizedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T => {
  return useCallback(callback, deps);
};

// 深度比较的 useMemo
export const useDeepMemo = <T>(
  factory: () => T,
  deps: React.DependencyList
): T => {
  const ref = useRef<{ deps: React.DependencyList; value: T }>(null);

  if (!ref.current || !deepEqual(ref.current.deps, deps)) {
    ref.current = { deps, value: factory() };
  }

  return ref.current.value;
};

// 深度比较函数
const deepEqual = (a: any, b: any): boolean => {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (typeof a !== typeof b) return false;

  if (typeof a === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    for (const key of keysA) {
      if (!keysB.includes(key) || !deepEqual(a[key], b[key])) {
        return false;
      }
    }
    
    return true;
  }

  return false;
};

// 虚拟滚动 Hook
export const useVirtualScroll = (
  itemCount: number,
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const scrollTop = useRef(0);

  const visibleRange = useMemo(() => {
    const start = Math.max(0, Math.floor(scrollTop.current / itemHeight) - overscan);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(itemCount - 1, start + visibleCount + overscan * 2);
    
    return { start, end };
  }, [itemCount, itemHeight, containerHeight, overscan, scrollTop.current]);

  const onScroll = useCallback((event: any) => {
    scrollTop.current = event.nativeEvent.contentOffset.y;
  }, []);

  const getItemStyle = useCallback((index: number) => ({
    position: 'absolute' as const,
    top: index * itemHeight,
    height: itemHeight,
    left: 0,
    right: 0,
  }), [itemHeight]);

  const containerStyle = useMemo(() => ({
    height: itemCount * itemHeight,
  }), [itemCount, itemHeight]);

  return {
    visibleRange,
    onScroll,
    getItemStyle,
    containerStyle,
  };
};

// 图片懒加载 Hook
export const useLazyImage = (src: string, placeholder?: string) => {
  const imgRef = useRef<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const imageSrc = useMemo(() => {
    if (!isInView) return placeholder;
    return src;
  }, [isInView, src, placeholder]);

  const onLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  return {
    ref: imgRef,
    src: imageSrc,
    isLoaded,
    onLoad,
  };
};

// 网络状态 Hook
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
};

// 内存使用监控 Hook
export const useMemoryMonitor = (interval: number = 5000) => {
  const [memoryInfo, setMemoryInfo] = useState<any>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        setMemoryInfo((performance as any).memory);
      }
    };

    updateMemoryInfo();
    const timer = setInterval(updateMemoryInfo, interval);

    return () => clearInterval(timer);
  }, [interval]);

  return memoryInfo;
};

// 组件挂载状态 Hook
export const useIsMounted = () => {
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return useCallback(() => isMountedRef.current, []);
};

// 安全的异步状态更新 Hook
export const useSafeAsyncState = <T>(initialState: T) => {
  const [state, setState] = useState<T>(initialState);
  const isMounted = useIsMounted();

  const setSafeState = useCallback((newState: T | ((prevState: T) => T)) => {
    if (isMounted()) {
      setState(newState);
    }
  }, [isMounted]);

  return [state, setSafeState] as const;
};

// 批量状态更新 Hook
export const useBatchedState = <T extends Record<string, any>>(initialState: T) => {
  const [state, setState] = useState<T>(initialState);
  const pendingUpdates = useRef<Partial<T>>({});
  const timeoutRef = useRef<NodeJS.Timeout>(null);

  const batchedSetState = useCallback((updates: Partial<T>) => {
    pendingUpdates.current = { ...pendingUpdates.current, ...updates };

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setState(prevState => ({ ...prevState, ...pendingUpdates.current }));
      pendingUpdates.current = {};
    }, 0);
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [state, batchedSetState] as const;
};
