/**
 * 测试辅助工具
 */

import { DataSourceItem } from '../types';

// 模拟数据生成器
export const createMockNewsArticle = (overrides: Partial<DataSourceItem> = {}): DataSourceItem => ({
  id: `article_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  title: '测试新闻标题',
  summary: '这是一个测试新闻的摘要内容',
  content: '这是测试新闻的详细内容。包含了足够的文字来测试阅读时间计算和内容显示。',
  cover: 'https://example.com/test-image.jpg',
  publishTime: new Date().toISOString(),
  author: '测试作者',
  readTime: 3,
  tags: ['测试', '区块链'],
  category: {
    id: 'news',
    name: '新闻资讯',
    slug: 'news',
    color: '#007AFF',
    icon: 'newspaper'
  },
  isFavorite: false,
  engagement: {
    views: 100,
    likes: 10,
    shares: 5,
  },
  ...overrides,
} as any);

export const createMockFlashNews = (overrides: Partial<DataSourceItem> = {}): DataSourceItem => ({
  id: `flash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  title: '测试快讯标题',
  content: '这是一个测试快讯的内容',
  publishTime: new Date().toISOString(),
  source: '测试数据源',
  dataType: 'flash',
  tags: ['快讯', '测试'],
  author: '测试作者',
  metadata: {
    priority: 'normal',
    isBreaking: false,
  },
  ...overrides,
});

export const createMockTweet = (overrides: Partial<DataSourceItem> = {}): DataSourceItem => ({
  id: `tweet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  title: '@测试用户',
  tweetText: '这是一条测试推文的内容 #区块链 #测试',
  TweetUrl: 'https://twitter.com/test/status/123456789',
  publishTime: new Date().toISOString(),
  source: 'twitter',
  dataType: 'tweet',
  author: '测试用户',
  metadata: {
    hotScore: 50,
  },
  engagement: {
    likes: 20,
    shares: 5,
    replies: 3,
  },
  ...overrides,
});

export const createMockDataSourceItem = (overrides: Partial<DataSourceItem> = {}): DataSourceItem => ({
  id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  title: '测试数据项标题',
  content: '测试数据项内容',
  publishTime: new Date().toISOString(),
  source: '测试数据源',
  dataType: 'article',
  tags: ['测试'],
  author: '测试作者',
  metadata: {
    readCount: 100,
    likeCount: 10,
    shareCount: 5,
  },
  ...overrides,
});

// 批量生成模拟数据
export const createMockNewsArticles = (count: number): DataSourceItem[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockNewsArticle({
      id: `article_${index}`,
      title: `测试新闻标题 ${index + 1}`,
      publishTime: new Date(Date.now() - index * 60000).toISOString(),
    })
  );
};

export const createMockFlashNewsList = (count: number): DataSourceItem[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockFlashNews({
      id: `flash_${index}`,
      title: `测试快讯标题 ${index + 1}`,
      publishTime: new Date(Date.now() - index * 30000).toISOString(),
      metadata: {
        priority: index % 3 === 0 ? 'high' : 'normal',
      },
    })
  );
};

export const createMockTweets = (count: number): DataSourceItem[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockTweet({
      id: `tweet_${index}`,
      tweetText: `测试推文内容 ${index + 1} #区块链`,
      publishTime: new Date(Date.now() - index * 45000).toISOString(),
      metadata: {
        hotScore: Math.floor(Math.random() * 100),
      },
    })
  );
};

// API 响应模拟器
export const createMockApiResponse = <T>(data: T, success: boolean = true) => ({
  success,
  data,
  message: success ? '操作成功' : '操作失败',
  timestamp: new Date().toISOString(),
});

export const createMockPaginatedResponse = <T>(
  items: T[],
  page: number = 1,
  limit: number = 20
) => ({
  items: items.slice((page - 1) * limit, page * limit),
  total: items.length,
  page,
  limit,
  hasMore: page * limit < items.length,
  totalPages: Math.ceil(items.length / limit),
});

// 延迟函数（用于模拟网络延迟）
export const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

// 随机失败模拟器
export const randomFailure = (failureRate: number = 0.1): boolean => 
  Math.random() < failureRate;

// 模拟网络请求
export const mockApiCall = async <T>(
  data: T,
  options: {
    delay?: number;
    failureRate?: number;
    errorMessage?: string;
  } = {}
): Promise<T> => {
  const { delay: delayMs = 500, failureRate = 0, errorMessage = '网络请求失败' } = options;

  await delay(delayMs);

  if (randomFailure(failureRate)) {
    throw new Error(errorMessage);
  }

  return data;
};

// 测试数据验证器
export const validateNewsArticle = (article: any): article is DataSourceItem => {
  return (
    typeof article === 'object' &&
    typeof article.id === 'string' &&
    typeof article.title === 'string' &&
    typeof article.summary === 'string' &&
    typeof article.publishTime === 'string' &&
    Array.isArray(article.tags)
  );
};

export const validateFlashNews = (flash: any): flash is DataSourceItem => {
  return (
    typeof flash === 'object' &&
    typeof flash.id === 'string' &&
    typeof flash.title === 'string' &&
    typeof flash.content === 'string' &&
    typeof flash.publishTime === 'string' &&
    flash.dataType === 'flash'
  );
};

export const validateTweet = (tweet: any): tweet is DataSourceItem => {
  return (
    typeof tweet === 'object' &&
    typeof tweet.id === 'string' &&
    typeof tweet.tweetText === 'string' &&
    typeof tweet.TweetUrl === 'string' &&
    typeof tweet.publishTime === 'string' &&
    tweet.dataType === 'tweet'
  );
};

// 性能测试辅助
export const measurePerformance = async <T>(
  operation: () => Promise<T>,
  label: string = 'Operation'
): Promise<{ result: T; duration: number }> => {
  const start = performance.now();
  const result = await operation();
  const duration = performance.now() - start;
  
  console.log(`${label} took ${duration.toFixed(2)}ms`);
  
  return { result, duration };
};

// 内存使用测试
export const measureMemoryUsage = (operation: () => void, label: string = 'Operation') => {
  const before = (performance as any).memory?.usedJSHeapSize || 0;
  operation();
  const after = (performance as any).memory?.usedJSHeapSize || 0;
  const diff = after - before;
  
  console.log(`${label} memory usage: ${diff} bytes`);
  
  return diff;
};

// 异步操作测试辅助
export const waitFor = async (
  condition: () => boolean,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> => {
  const start = Date.now();
  
  while (!condition() && Date.now() - start < timeout) {
    await delay(interval);
  }
  
  if (!condition()) {
    throw new Error(`Condition not met within ${timeout}ms`);
  }
};
