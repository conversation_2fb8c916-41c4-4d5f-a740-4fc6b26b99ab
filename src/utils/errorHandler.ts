import { AppError } from '../types';

/**
 * 错误处理工具
 */

// 错误类型枚举
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  TRANSFORM_ERROR = 'TRANSFORM_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 错误消息映射
const ERROR_MESSAGES: Record<ErrorType, string> = {
  [ErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ErrorType.API_ERROR]: 'API 请求失败，请稍后重试',
  [ErrorType.VALIDATION_ERROR]: '数据验证失败',
  [ErrorType.CACHE_ERROR]: '缓存操作失败',
  [ErrorType.TRANSFORM_ERROR]: '数据转换失败',
  [ErrorType.UNKNOWN_ERROR]: '未知错误',
};

// 创建应用错误
export const createAppError = (
  type: ErrorType,
  message?: string,
  details?: any
): AppError => {
  return {
    code: type,
    message: message || ERROR_MESSAGES[type],
    details,
  };
};

// 错误分类器
export const classifyError = (error: any): ErrorType => {
  if (!error) return ErrorType.UNKNOWN_ERROR;

  // 网络错误
  if (error.code === 'NETWORK_ERROR' || 
      error.message?.includes('网络连接失败') ||
      error.message?.includes('Network Error') ||
      !error.response) {
    return ErrorType.NETWORK_ERROR;
  }

  // API 错误
  if (error.response) {
    return ErrorType.API_ERROR;
  }

  // 验证错误
  if (error.name === 'ValidationError' || 
      error.message?.includes('validation')) {
    return ErrorType.VALIDATION_ERROR;
  }

  // 缓存错误
  if (error.message?.includes('cache') || 
      error.message?.includes('缓存')) {
    return ErrorType.CACHE_ERROR;
  }

  // 转换错误
  if (error.message?.includes('transform') || 
      error.message?.includes('转换')) {
    return ErrorType.TRANSFORM_ERROR;
  }

  return ErrorType.UNKNOWN_ERROR;
};

// 错误处理器
export const handleError = (error: any, context?: string): AppError => {
  const errorType = classifyError(error);
  const contextMessage = context ? `${context}: ` : '';
  
  console.error(`${contextMessage}${error.message || error}`, error);

  return createAppError(
    errorType,
    `${contextMessage}${error.message || ERROR_MESSAGES[errorType]}`,
    {
      originalError: error,
      context,
      timestamp: new Date().toISOString(),
    }
  );
};

// 重试机制
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  context?: string
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw handleError(error, context);
      }

      // 指数退避
      const waitTime = delay * Math.pow(2, attempt - 1);
      console.warn(`${context || 'Operation'} failed (attempt ${attempt}/${maxRetries}), retrying in ${waitTime}ms...`);
      
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  throw handleError(lastError, context);
};

// 安全的异步操作包装器
export const safeAsync = async <T>(
  operation: () => Promise<T>,
  fallback?: T,
  context?: string
): Promise<T | undefined> => {
  try {
    return await operation();
  } catch (error) {
    const appError = handleError(error, context);
    console.error('Safe async operation failed:', appError);
    return fallback;
  }
};

// 数据验证器
export const validateData = <T>(
  data: any,
  validator: (data: any) => data is T,
  context?: string
): T => {
  if (!validator(data)) {
    throw createAppError(
      ErrorType.VALIDATION_ERROR,
      `${context || 'Data'} validation failed`,
      { data }
    );
  }
  return data;
};

// 类型守卫
export const isValidResponse = (response: any): boolean => {
  return response && 
         typeof response === 'object' && 
         typeof response.success === 'boolean';
};

export const isValidPaginatedResponse = (response: any): boolean => {
  return isValidResponse(response) &&
         response.data &&
         Array.isArray(response.data.items) &&
         typeof response.data.total === 'number';
};

// 错误边界组件的错误处理
export const handleComponentError = (error: Error, errorInfo: any) => {
  const appError = createAppError(
    ErrorType.UNKNOWN_ERROR,
    `Component error: ${error.message}`,
    {
      error: error.toString(),
      errorInfo,
      stack: error.stack,
    }
  );

  console.error('Component Error:', appError);
  
  // 这里可以添加错误上报逻辑
  // reportError(appError);
  
  return appError;
};

// 网络状态检查
export const checkNetworkStatus = (): boolean => {
  // 在 React Native 中，可以使用 @react-native-community/netinfo
  // 这里提供一个基础实现
  return navigator.onLine !== false;
};

// 错误恢复策略
export const getRecoveryStrategy = (errorType: ErrorType): string[] => {
  const strategies: Record<ErrorType, string[]> = {
    [ErrorType.NETWORK_ERROR]: [
      '检查网络连接',
      '尝试切换网络',
      '稍后重试',
    ],
    [ErrorType.API_ERROR]: [
      '刷新页面',
      '清除缓存',
      '联系技术支持',
    ],
    [ErrorType.VALIDATION_ERROR]: [
      '检查输入数据',
      '重新填写表单',
    ],
    [ErrorType.CACHE_ERROR]: [
      '清除应用缓存',
      '重启应用',
    ],
    [ErrorType.TRANSFORM_ERROR]: [
      '刷新数据',
      '重新加载',
    ],
    [ErrorType.UNKNOWN_ERROR]: [
      '重启应用',
      '联系技术支持',
    ],
  };

  return strategies[errorType] || strategies[ErrorType.UNKNOWN_ERROR];
};

// 错误上报（可选）
export const reportError = (error: AppError): void => {
  // 这里可以集成错误上报服务，如 Sentry、Bugsnag 等
  console.log('Error reported:', error);
  
  // 示例：发送到错误收集服务
  // if (process.env.NODE_ENV === 'production') {
  //   fetch('/api/errors', {
  //     method: 'POST',
  //     headers: { 'Content-Type': 'application/json' },
  //     body: JSON.stringify(error),
  //   }).catch(console.error);
  // }
};
