/**
 * 时间格式化工具函数
 * 将ISO时间戳转换为用户友好的相对时间格式
 */

export const formatRelativeTime = (isoString: string): string => {
  try {
    const date = new Date(isoString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // 如果时间差为负数或无效，返回"刚刚"
    if (diffInSeconds < 0 || isNaN(diffInSeconds)) {
      return '刚刚';
    }

    // 小于1分钟
    if (diffInSeconds < 60) {
      return '刚刚';
    }

    // 小于1小时
    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}分钟前`;
    }

    // 小于1天
    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}小时前`;
    }

    // 小于7天
    if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}天前`;
    }

    // 小于30天
    if (diffInSeconds < 2592000) {
      const weeks = Math.floor(diffInSeconds / 604800);
      return `${weeks}周前`;
    }

    // 小于1年
    if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000);
      return `${months}个月前`;
    }

    // 超过1年
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years}年前`;
  } catch (error) {
    console.error('时间格式化错误:', error);
    return '时间未知';
  }
};

/**
 * 格式化为简短的日期时间格式
 * 用于需要显示具体时间的场景
 */
export const formatShortDateTime = (isoString: string): string => {
  try {
    const date = new Date(isoString);
    const now = new Date();
    
    // 检查是否是今天
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      // 今天只显示时间
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    }
    
    // 检查是否是今年
    const isThisYear = date.getFullYear() === now.getFullYear();
    
    if (isThisYear) {
      // 今年显示月日和时间
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    }
    
    // 其他年份显示完整日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '日期未知';
  }
};

/**
 * 格式化为完整的日期时间格式
 * 用于详情页面等需要显示完整时间的场景
 */
export const formatFullDateTime = (isoString: string): string => {
  try {
    const date = new Date(isoString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (error) {
    console.error('完整时间格式化错误:', error);
    return '时间未知';
  }
};
