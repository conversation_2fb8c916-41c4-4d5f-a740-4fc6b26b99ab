import {
  NewsArticle,
  FlashNewsItem,
  TweetItem,
  FinanceItem,
  PriceItem,
  ToolEventItem,
  ContentItem,
  DataSourceItem,
  NewsCategory,
} from "../types";

/**
 * 数据转换工具函数
 * 将后端 DataSourceItem 转换为前端对应的数据类型
 */

// 计算阅读时间（基于内容长度）
export const calculateReadTime = (content: string): number => {
  if (!content) return 1;
  const wordsPerMinute = 200; // 平均阅读速度
  const wordCount = content.length / 5; // 中文按字符数/5估算词数
  return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
};

// 将数据类型映射为前端分类
export const mapDataTypeToCategory = (dataType: string): NewsCategory => {
  const categoryMap: Record<string, NewsCategory> = {
    article: {
      id: "news",
      name: "新闻资讯",
      slug: "news",
      color: "#007AFF",
      icon: "newspaper",
    },
    news: {
      id: "news",
      name: "新闻资讯",
      slug: "news",
      color: "#007AFF",
      icon: "newspaper",
    },
    post: {
      id: "news",
      name: "新闻资讯",
      slug: "news",
      color: "#007AFF",
      icon: "newspaper",
    },
    flash: {
      id: "flash",
      name: "快讯",
      slug: "flash",
      color: "#FF3B30",
      icon: "flash",
    },
    depth: {
      id: "analysis",
      name: "深度分析",
      slug: "analysis",
      color: "#34C759",
      icon: "analytics",
    },
    column: {
      id: "analysis",
      name: "深度分析",
      slug: "analysis",
      color: "#34C759",
      icon: "analytics",
    },
    subject: {
      id: "analysis",
      name: "深度分析",
      slug: "analysis",
      color: "#34C759",
      icon: "analytics",
    },
    price: {
      id: "price",
      name: "价格数据",
      slug: "price",
      color: "#FF9500",
      icon: "trending-up",
    },
    tweet: {
      id: "social",
      name: "社交观点",
      slug: "social",
      color: "#5856D6",
      icon: "logo-twitter",
    },
    finance: {
      id: "finance",
      name: "融资信息",
      slug: "finance",
      color: "#AF52DE",
      icon: "cash",
    },
    financing: {
      id: "finance",
      name: "融资信息",
      slug: "finance",
      color: "#AF52DE",
      icon: "cash",
    },
    fundraising: {
      id: "finance",
      name: "融资信息",
      slug: "finance",
      color: "#AF52DE",
      icon: "cash",
    },
    tools: {
      id: "tools",
      name: "工具资源",
      slug: "tools",
      color: "#32D74B",
      icon: "construct",
    },
    event: {
      id: "events",
      name: "事件日历",
      slug: "events",
      color: "#FF2D92",
      icon: "calendar",
    },
    calendars: {
      id: "events",
      name: "事件日历",
      slug: "events",
      color: "#FF2D92",
      icon: "calendar",
    },
    dayNews: {
      id: "daily",
      name: "日报",
      slug: "daily",
      color: "#30D158",
      icon: "today",
    },
  };

  return (
    categoryMap[dataType] || {
      id: "general",
      name: "综合",
      slug: "general",
      color: "#8E8E93",
      icon: "document-text",
    }
  );
};

// 转换为新闻文章
export const transformToNewsArticle = (item: DataSourceItem): NewsArticle => {
  return item
};

// 转换为快讯
export const transformToFlashNews = (item: DataSourceItem): FlashNewsItem => {
  return item
};

// 转换为推文
export const transformToTweet = (item: DataSourceItem): TweetItem => {
  return item
};

// 转换为融资信息
export const transformToFinance = (item: DataSourceItem): FinanceItem => {
  return {
    id: item.id,
    title: item.title,
    content: item.content || item.description || "",
    publishTime: item.publishTime,
    source: item.source,
    dataType: item.dataType as "finance" | "financing" | "fundraising",
    tags: item.tags,
    metadata: {
      amount: item.metadata?.amount || item.metadata?.money,
      round: item.metadata?.round,
      investors: item.metadata?.investors || item.metadata?.invest,
      field: item.metadata?.field,
      ...item.metadata,
    },
  };
};

// 转换为价格数据
export const transformToPrice = (item: DataSourceItem): PriceItem => {
  return item;
};

// 转换为工具/事件
export const transformToToolEvent = (item: DataSourceItem): ToolEventItem => {
  return {
    id: item.id,
    title: item.title,
    content: item.content,
    description: item.description,
    url: item.url,
    publishTime: item.publishTime,
    source: item.source,
    dataType: item.dataType as "tools" | "event" | "calendars" | "dayNews",
    tags: item.tags,
    metadata: item.metadata,
  };
};

// 通用转换函数
export const transformDataSourceItem = (item: DataSourceItem): ContentItem => {
  switch (item.dataType) {
    case "flash":
      return transformToFlashNews(item);
    case "tweet":
      return transformToTweet(item);
    case "finance":
    case "financing":
    case "fundraising":
      return transformToFinance(item);
    case "price":
      return transformToPrice(item);
    case "tools":
    case "event":
    case "calendars":
    case "dayNews":
      return transformToToolEvent(item);
    default:
      // 默认转换为新闻文章
      return transformToNewsArticle(item);
  }
};
