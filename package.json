{"name": "chainmix", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --tunnel", "dev:web": "expo start --web", "android": "expo start --android", "build:android": "eas build --platform android --local", "build:android-apk": "eas build --platform android --profile production-apk", "build:android-apk:local": "eas build --platform android --profile production-apk --local", "build:android-preview": "eas build --platform android --profile preview --local", "build:ios": "eas build --platform ios --local", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@tamagui/animations-react-native": "^1.129.4", "@tamagui/config": "^1.129.4", "@tamagui/core": "^1.129.4", "@tamagui/font-inter": "^1.129.4", "@tamagui/theme-base": "^1.129.4", "axios": "^1.10.0", "expo": "~53.0.13", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eas-cli": "^16.13.2", "typescript": "~5.8.3"}, "private": true, "packageManager": "pnpm@10.6.5+sha512.cdf928fca20832cd59ec53826492b7dc25dc524d4370b6b4adbf65803d32efaa6c1c88147c0ae4e8d579a6c9eec715757b50d4fa35eea179d868eada4ed043af", "pnpm": {"ignoredBuiltDependencies": ["dtrace-provider"]}}