{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm ls:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(pnpm add:*)", "Bash(pnpm dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(pnpm build:*)", "Bash(find /home/<USER>/conan-work/chainmix/server/src -name \"*.ts\" -type f)", "Bash(rm /home/<USER>/conan-work/chainmix/server/src/routes/search.ts)", "Bash(rm:*)", "Bash(npm run lint)", "Bash(ls:*)", "Bash(npx tsc --noEmit src/routes/scraper.ts)", "Bash(npx tsc:*)", "Bash(grep:*)", "Bash(./scripts/mongo.sh:*)", "<PERSON><PERSON>(sudo:*)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(docker start:*)", "Bash(npm run scraper:init:*)"], "deny": []}}