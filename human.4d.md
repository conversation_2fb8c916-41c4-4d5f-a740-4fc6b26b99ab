您是一位专业的 React Native 开发专家，精通 Expo 技术栈、前端架构分析和文档撰写。您的任务是分析一个基于 React Native + Expo 的跨平台移动应用前端源码（位于当前工程目录），该应用为用户提供区块链和加密货币资讯。分析项目的架构、模块、技术架构和业务流程，并生成一份详细的 Markdown 技术文档，输出到 `docs/front.md`。请按照以下要求完成：

### 任务要求
1. **分析项目架构**：
   - 描述项目的文件结构（如 `App.js`、`screens/`、`components/`）。
   - 说明导航结构（如 React Navigation 的 Stack 或 Tab 导航）。
   - 概述组件层次（屏幕、组件、上下文）。
2. **分析模块**：
   - 列出主要功能模块（如屏幕、组件、服务）。
   - 描述每个模块的职责（如 `NewsScreen` 展示资讯列表）。
   - 提供伪代码或逻辑描述
3. **分析技术架构**：
   - 描述使用的技术栈和库（如 React Native、Expo、Axios、React Navigation）。
   - 说明每个库的作用（如 Axios 处理 API 请求）。
   - 概述构建和开发工具（如 Expo CLI、TypeScript）。
4. **分析业务流程**：
   - 描述用户交互和数据流（如打开应用 → 浏览资讯 → 查看详情）。
   - 说明数据来源和处理（如调用 API、渲染列表）。
   - 提供文字描述或伪代码，必要时附带流程图（Markdown 格式）。
5. **输出格式**：
   - 使用清晰的 Markdown 结构，包含标题、列表、表格、伪代码或流程图。
   - 确保内容适合前端开发人员，语言简洁且专业。
   - 文件路径：`docs/front.md`。

------------------------------------------------------------------------------
您是一位专业的 Node.js 和 TypeScript 后端开发专家，精通 Express 框架、现代架构设计和行业最佳实践。您的任务是为一个基于 React Native + Expo 的区块链资讯移动应用（前端描述见 `docs/front.md`）设计并搭建一个配套的 Node.js + Express + TypeScript 后端服务。新建后端项目需采用当前最流行的架构、实现行业最佳实践，并支持前端的新闻列表、价格跟踪和搜索功能。项目应在 `server/` 目录中创建，包含完整的代码文件和配置，并生成说明文档追加到 `docs/back.md`。请按照以下要求完成：

### 任务要求
1. **创建项目结构**：
   - 在 `server/` 目录初始化 Node.js + TypeScript 项目。
   - 包含以下文件和目录：
     - `src/app.ts`：Express 服务器入口。
     - `src/routes/`：API 路由（如 `news.ts`、`prices.ts`）。
     - `src/controllers/`：控制器逻辑。
     - `src/services/`：业务逻辑（如调用外部 API）。
     - `src/types/`：TypeScript 类型定义。
     - `src/middlewares/`：中间件（如错误处理、验证）。
     - `src/utils/`：工具函数（如日志、缓存）。
     - `package.json`：依赖和脚本。
     - `tsconfig.json`：TypeScript 配置。
     - `.env`：环境变量。
     - `.eslintrc.js`：ESLint 配置。
     - `.prettierrc`：Prettier 配置。
     - 可以增加其他你认为需要增加的配置
2. **设计架构**：
   - 采用分层架构：路由 → 控制器 → 服务 → 外部 API。
   - 实现 RESTful API，支持以下端点：
     - `GET /news`：获取区块链资讯列表。
     - `GET /prices`：获取加密货币价格。
     - `GET /search?q=keyword`：搜索资讯。
     - 可以阅读前端源码 src 目录下文件决定路由
     - 可以增加其他你认为需要增加的路由
   - 数据来源：
     - 代理外部 API（如 CoinGecko、NewsAPI）。
3. **实现最佳实践**：
   - **类型安全**：使用 TypeScript 和 Zod 验证输入。
   - **错误处理**：全局错误中间件，返回标准化的 JSON 错误。
   - **安全性**：使用 Helmet、CORS，限制请求速率。
   - **日志**：使用 Winston 记录请求和错误。
   - **测试**：配置 Jest，包含至少一个单元测试用例。
   - **代码规范**：使用 ESLint 和 Prettier。
   - **环境管理**：使用 dotenv 加载 `.env` 文件。
   - **性能**：实现简单的内存缓存（如 `node-cache`）。
4. **生成代码**：
   - 提供以下文件的完整代码：
     - `src/app.ts`：Express 服务器配置。
     - `src/routes/news.ts`：新闻路由。
     - `src/controllers/news.ts`：新闻控制器。
     - `src/services/news.ts`：新闻服务（调用 NewsAPI）。
     - `src/middlewares/error.ts`：错误处理中间件。
     - `src/types/index.ts`：类型定义。
     - `package.json`, `tsconfig.json`, `.env`, `.eslintrc.js`, `.prettierrc`。
     - 可以增加其他你认为需要增加的文件
   - 包含伪代码或注释说明逻辑。
5. **生成文档**：
   - 追加到 `docs/back.md`，包含：
     - 项目结构：文件和目录说明。
     - API 端点：URL、方法、请求/响应格式。
     - 运行说明：安装、启动、测试步骤。
     - 最佳实践：实现的行业标准。
   - 使用 Markdown 格式，包含标题、列表、表格、代码片段。
