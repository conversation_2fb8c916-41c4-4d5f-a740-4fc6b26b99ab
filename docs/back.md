# ChainMix 后端技术文档

> 基于 Node.js + Express + TypeScript 的区块链资讯应用后端服务

## 目录

1. [项目概述](#项目概述)
2. [架构设计](#架构设计)
3. [技术栈](#技术栈)
4. [API 端点](#api-端点)
5. [数据模型](#数据模型)
6. [外部服务集成](#外部服务集成)
7. [性能优化](#性能优化)
8. [安全机制](#安全机制)
9. [运行说明](#运行说明)
10. [最佳实践](#最佳实践)

---

## 项目概述

ChainMix 后端服务是一个现代化的 RESTful API 服务器，专为区块链资讯移动应用提供数据支持。该服务采用分层架构设计，集成多个外部 API，提供高性能、高可用的数据服务。

### 核心功能
- 📰 **新闻资讯服务**: 集成 NewsAPI，提供实时区块链新闻
- 💰 **价格数据服务**: 集成 CoinGecko API，提供加密货币价格信息
- 🔍 **智能搜索**: 支持跨内容类型的全文搜索
- ⚡ **高性能缓存**: 多层缓存策略，减少外部 API 调用
- 🔒 **企业级安全**: 完整的安全中间件和验证机制
- 📊 **监控和日志**: 结构化日志和性能监控

---

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端 (React Native)                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      路由层 (Routes)                        │
│  • news.ts     • prices.ts     • search.ts                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     控制器层 (Controllers)                   │
│  • 请求验证     • 业务编排     • 响应格式化                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     服务层 (Services)                       │
│  • 业务逻辑     • 数据转换     • 缓存管理                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     外部 API 层                             │
│  • NewsAPI.org    • CoinGecko API    • 其他服务            │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构

```
server/
├── src/
│   ├── app.ts                 # Express 应用主入口
│   ├── controllers/           # 控制器层
│   │   ├── news.ts           # 新闻控制器
│   │   └── prices.ts         # 价格控制器
│   ├── middlewares/           # 中间件
│   │   ├── error.ts          # 错误处理中间件
│   │   ├── validation.ts     # 数据验证中间件
│   │   └── logger.ts         # 日志中间件
│   ├── routes/                # 路由定义
│   │   ├── news.ts           # 新闻路由
│   │   ├── prices.ts         # 价格路由
│   │   ├── categories.ts     # 分类路由
│   │   └── search.ts         # 搜索路由
│   ├── services/              # 业务服务层
│   │   ├── news.ts           # 新闻服务
│   │   └── prices.ts         # 价格服务
│   ├── types/                 # TypeScript 类型定义
│   │   └── index.ts          # 统一类型导出
│   ├── utils/                 # 工具函数
│   │   ├── logger.ts         # 日志工具
│   │   └── cache.ts          # 缓存管理
│   └── tests/                 # 测试文件
│       ├── setup.ts          # 测试设置
│       └── news.test.ts      # 新闻模块测试
├── package.json               # 项目依赖
├── tsconfig.json             # TypeScript 配置
├── jest.config.js            # Jest 测试配置
├── .eslintrc.js              # ESLint 配置
├── .prettierrc               # Prettier 配置
└── .env                      # 环境变量
```

---

## 技术栈

### 核心技术

| 技术 | 版本 | 作用 |
|------|------|------|
| **Node.js** | 18+ | 服务器运行时环境 |
| **Express.js** | 4.19+ | Web 应用框架 |
| **TypeScript** | 5.3+ | 静态类型检查 |
| **Zod** | 3.22+ | 运行时数据验证 |

### 安全和中间件

| 库名 | 版本 | 作用 |
|------|------|------|
| **Helmet** | 7.1+ | 安全头部设置 |
| **CORS** | 2.8+ | 跨域资源共享 |
| **express-rate-limit** | 7.1+ | 请求频率限制 |
| **compression** | 1.7+ | 响应压缩 |

### 日志和监控

| 库名 | 版本 | 作用 |
|------|------|------|
| **Winston** | 3.11+ | 结构化日志记录 |
| **node-cache** | 5.1+ | 内存缓存管理 |

### 外部集成

| 服务 | 作用 |
|------|------|
| **NewsAPI.org** | 区块链新闻数据源 |
| **CoinGecko API** | 加密货币价格数据 |
| **Axios** | HTTP 客户端 |

### 开发工具

| 工具 | 版本 | 作用 |
|------|------|------|
| **Jest** | 29.7+ | 单元测试框架 |
| **ESLint** | 8.56+ | 代码质量检查 |
| **Prettier** | 3.1+ | 代码格式化 |
| **ts-node-dev** | 2.0+ | 开发时热重载 |

---

## API 端点

### 基础信息

**Base URL**: `http://localhost:3000/api`

**通用响应格式**:
```typescript
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: string;
}
```

### 新闻资讯端点

#### 1. 获取新闻列表

```http
GET /api/news
```

**查询参数**:
| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `page` | number | 1 | 页码 |
| `limit` | number | 20 | 每页数量 (最大 100) |
| `category` | string | - | 分类筛选 |
| `sortBy` | string | publishedAt | 排序字段 |
| `sortOrder` | string | desc | 排序方向 |

**响应示例**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "abc123",
        "title": "Bitcoin 突破新高...",
        "summary": "加密货币市场...",
        "content": "完整文章内容...",
        "imageUrl": "https://example.com/image.jpg",
        "category": {
          "id": "1",
          "name": "Cryptocurrency",
          "slug": "cryptocurrency",
          "color": "#FF9500",
          "icon": "logo-bitcoin"
        },
        "publishedAt": "2025-07-10T12:00:00Z",
        "author": "CoinDesk",
        "readTime": 5,
        "tags": ["bitcoin", "cryptocurrency", "market"],
        "source": {
          "name": "CoinDesk",
          "url": "https://coindesk.com/..."
        }
      }
    ],
    "total": 1000,
    "page": 1,
    "limit": 20,
    "hasMore": true,
    "totalPages": 50
  },
  "message": "News articles retrieved successfully",
  "timestamp": "2025-07-10T12:00:00Z"
}
```

#### 2. 搜索新闻

```http
GET /api/news/search
```

**查询参数**:
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `q` | string | ✓ | 搜索关键词 |
| `page` | number | - | 页码 (默认 1) |
| `limit` | number | - | 每页数量 (默认 20，最大 50) |
| `category` | string | - | 分类筛选 |
| `sortBy` | string | - | 排序字段 (relevance/publishedAt) |

#### 3. 获取热门新闻

```http
GET /api/news/trending
```

**查询参数**:
| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `limit` | number | 10 | 返回数量 (最大 50) |

#### 4. 获取精选新闻

```http
GET /api/news/featured
```

#### 5. 获取新闻分类

```http
GET /api/news/categories
```

#### 6. 根据 ID 获取文章

```http
GET /api/news/:id
```

#### 7. 根据分类获取文章

```http
GET /api/news/category/:slug
```

### 加密货币价格端点

#### 1. 获取加密货币列表

```http
GET /api/prices
```

**查询参数**:
| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `vs_currency` | string | usd | 目标货币 |
| `order` | string | market_cap_desc | 排序方式 |
| `per_page` | number | 100 | 每页数量 (最大 250) |
| `page` | number | 1 | 页码 |
| `sparkline` | boolean | false | 是否包含走势图数据 |

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "bitcoin",
      "symbol": "BTC",
      "name": "Bitcoin",
      "image": "https://assets.coingecko.com/coins/images/1/large/bitcoin.png",
      "currentPrice": 45000.00,
      "marketCap": 850000000000,
      "marketCapRank": 1,
      "totalVolume": 25000000000,
      "high24h": 46000.00,
      "low24h": 44000.00,
      "priceChange24h": 1000.00,
      "priceChangePercentage24h": 2.27,
      "lastUpdated": "2025-07-10T12:00:00Z"
    }
  ],
  "message": "Cryptocurrency prices retrieved successfully",
  "timestamp": "2025-07-10T12:00:00Z"
}
```

#### 2. 获取热门加密货币

```http
GET /api/prices/trending
```

#### 3. 获取市值排名前列

```http
GET /api/prices/top
```

#### 4. 获取特定加密货币

```http
GET /api/prices/:id
```

#### 5. 获取价格历史

```http
GET /api/prices/:id/history
```

**查询参数**:
| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `vs_currency` | string | usd | 目标货币 |
| `days` | string | 7 | 时间范围 (1,7,14,30,90,180,365,max) |
| `interval` | string | - | 数据间隔 (minutely,hourly,daily) |

#### 6. 获取市场概览

```http
GET /api/prices/market-overview
```

### 搜索端点

#### 全局搜索

```http
GET /api/search
```

### 分类端点

#### 获取分类列表

```http
GET /api/categories
```

---

## 数据模型

### 新闻文章模型

```typescript
interface NewsArticle {
  id: string;                    // 文章唯一标识
  title: string;                 // 文章标题
  summary: string;               // 文章摘要
  content: string;               // 文章内容
  imageUrl?: string;             // 配图URL
  category: NewsCategory;        // 文章分类
  publishedAt: string;           // 发布时间
  author: string;                // 作者
  readTime: number;              // 阅读时间（分钟）
  tags: string[];                // 标签列表
  source: {                      // 来源信息
    name: string;
    url: string;
  };
  engagement?: {                 // 互动数据
    views: number;
    likes: number;
    shares: number;
  };
}
```

### 新闻分类模型

```typescript
interface NewsCategory {
  id: string;                    // 分类ID
  name: string;                  // 分类名称
  slug: string;                  // URL友好标识
  color: string;                 // 主题颜色
  icon: string;                  // 图标名称
  description?: string;          // 分类描述
}
```

### 加密货币模型

```typescript
interface CryptoCurrency {
  id: string;                    // 货币ID
  symbol: string;                // 交易符号
  name: string;                  // 货币名称
  image: string;                 // 图标URL
  currentPrice: number;          // 当前价格
  marketCap: number;             // 市值
  marketCapRank: number;         // 市值排名
  totalVolume: number;           // 24小时交易量
  high24h: number;               // 24小时最高价
  low24h: number;                // 24小时最低价
  priceChange24h: number;        // 24小时价格变化
  priceChangePercentage24h: number; // 24小时价格变化百分比
  lastUpdated: string;           // 最后更新时间
}
```

---

## 外部服务集成

### NewsAPI.org 集成

**配置信息**:
```typescript
{
  baseUrl: 'https://newsapi.org/v2',
  apiKey: process.env.NEWS_API_KEY,
  timeout: 10000,
  retries: 3,
  rateLimit: {
    requests: 1000,
    window: 24 * 60 * 60 * 1000 // 24小时
  }
}
```

**实现特性**:
- 智能分类映射：根据内容自动分配加密货币相关分类
- 内容过滤：过滤无效或删除的文章
- 数据转换：将 NewsAPI 格式转换为应用内部格式
- 缓存优化：实现分层缓存减少 API 调用

**服务方法**:
```typescript
class NewsService {
  // 获取文章列表
  async getArticles(page: number, limit: number, category?: string): Promise<{
    articles: NewsArticle[];
    total: number;
    hasMore: boolean;
  }>

  // 搜索文章
  async searchArticles(query: string, page: number, limit: number): Promise<{
    articles: NewsArticle[];
    total: number;
    hasMore: boolean;
  }>

  // 获取热门文章
  async getTrendingArticles(limit: number): Promise<NewsArticle[]>

  // 获取分类
  async getCategories(): Promise<NewsCategory[]>
}
```

### CoinGecko API 集成

**配置信息**:
```typescript
{
  baseUrl: 'https://api.coingecko.com/api/v3',
  apiKey: process.env.COINGECKO_API_KEY,
  timeout: 10000,
  retries: 3,
  rateLimit: {
    requests: 100,
    window: 60 * 1000 // 1分钟（免费版限制）
  }
}
```

**实现特性**:
- 价格数据同步：实时获取加密货币价格信息
- 历史数据：提供多时间周期的价格历史数据
- 市场统计：计算市场概览和统计信息
- 错误处理：完善的API错误处理和降级策略

**服务方法**:
```typescript
class PricesService {
  // 获取加密货币列表
  async getCryptocurrencies(page: number, limit: number, currency: string): Promise<{
    currencies: CryptoCurrency[];
    hasMore: boolean;
  }>

  // 获取特定加密货币
  async getCryptocurrency(id: string, currency: string): Promise<CryptoCurrency>

  // 获取价格历史
  async getPriceHistory(id: string, days: string, currency: string): Promise<CryptoPriceData>

  // 获取热门加密货币
  async getTrendingCryptocurrencies(): Promise<CryptoCurrency[]>
}
```

---

## 性能优化

### 缓存策略

**多层缓存架构**:
```typescript
class CacheManager {
  // L1: 内存缓存 (最快访问)
  private memoryCache: NodeCache;
  
  // 缓存配置
  constructor() {
    this.defaultTTL = 5 * 60; // 5分钟默认TTL
    this.checkPeriod = 10 * 60; // 10分钟清理周期
  }

  // 智能缓存策略
  public async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number
  ): Promise<T>
}
```

**缓存键命名规范**:
```typescript
export const cacheKeys = {
  news: {
    articles: (page: number, limit: number, category?: string) =>
      `news:articles:${page}:${limit}:${category || 'all'}`,
    search: (query: string, page: number, limit: number) =>
      `news:search:${encodeURIComponent(query)}:${page}:${limit}`,
    trending: (limit: number) => `news:trending:${limit}`,
  },
  prices: {
    coins: (page: number, limit: number, currency: string) =>
      `prices:coins:${page}:${limit}:${currency}`,
    history: (id: string, days: string, currency: string) =>
      `prices:history:${id}:${days}:${currency}`,
  },
};
```

**缓存TTL策略**:
| 数据类型 | TTL | 原因 |
|----------|-----|------|
| 新闻列表 | 5分钟 | 需要实时性 |
| 搜索结果 | 3分钟 | 搜索结果变化频繁 |
| 价格数据 | 1分钟 | 价格波动频繁 |
| 价格历史 | 30分钟 | 历史数据相对稳定 |
| 分类信息 | 1小时 | 分类变化很少 |

### HTTP 优化

**压缩和传输优化**:
```typescript
// 启用响应压缩
app.use(compression({
  level: 6,
  threshold: 1024,
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
}));
```

**请求优化**:
```typescript
// Axios 实例配置
const apiClient = axios.create({
  timeout: 10000,
  headers: {
    'User-Agent': 'ChainMix-Backend/1.0.0',
    'Accept-Encoding': 'gzip, deflate',
  },
});
```

### 数据库连接池 (未来扩展)

```typescript
// 为未来数据库集成预留的连接池配置
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  pool: {
    min: number;
    max: number;
    idle: number;
  };
}
```

---

## 安全机制

### 请求安全

**Helmet 安全头**:
```typescript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));
```

**CORS 配置**:
```typescript
const corsOptions = {
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  optionsSuccessStatus: 200,
};
```

### 频率限制

**智能频率限制**:
```typescript
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟窗口
  max: 100, // 每个IP最多100请求
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: 900,
  },
  standardHeaders: true,
  legacyHeaders: false,
});
```

### 输入验证

**Zod 验证模式**:
```typescript
// 新闻查询验证
export const GetNewsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  category: z.string().optional(),
  sortBy: z.enum(['publishedAt', 'relevance', 'popularity']).default('publishedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// 搜索查询验证
export const SearchNewsQuerySchema = z.object({
  q: z.string().min(1).max(100),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20),
});
```

### 错误处理

**分层错误处理**:
```typescript
// 自定义错误类
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public details?: Record<string, unknown>;

  constructor(message: string, statusCode: number, code: string, details?: Record<string, unknown>) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

// 全局错误处理中间件
export const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction): void => {
  // 记录错误日志
  logger.error('Server error occurred', {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
  });

  // 返回标准化错误响应
  const response: ErrorResponse = {
    success: false,
    data: null,
    error: error.message,
    timestamp: new Date().toISOString(),
  };

  res.status(error instanceof AppError ? error.statusCode : 500).json(response);
};
```

---

## 运行说明

### 环境准备

**系统要求**:
- Node.js >= 18.0.0
- npm >= 9.0.0
- 操作系统: Linux, macOS, Windows

**API 密钥申请**:

1. **NewsAPI.org 密钥**:
   - 访问 [NewsAPI.org](https://newsapi.org)
   - 注册开发者账号
   - 获取免费 API 密钥 (1000请求/天)

2. **CoinGecko API 密钥**:
   - 访问 [CoinGecko API](https://coingecko.com/api)
   - 免费版本无需密钥（有频率限制）
   - 付费版本获得更高频率限制

### 安装步骤

```bash
# 1. 克隆仓库
git clone <repository-url>
cd chainmix/server

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env
nano .env

# 4. 构建项目
npm run build

# 5. 启动服务
npm start
```

### 环境变量配置

```bash
# 服务器配置
NODE_ENV=production
PORT=3000

# CORS 配置
CORS_ORIGIN=https://yourdomain.com,https://app.yourdomain.com

# API 密钥
NEWS_API_KEY=your_actual_newsapi_key
COINGECKO_API_KEY=your_actual_coingecko_key

# 频率限制
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 缓存配置
CACHE_TTL_MINUTES=5
CACHE_CHECK_PERIOD_MINUTES=10

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 外部API配置
NEWS_API_BASE_URL=https://newsapi.org/v2
COINGECKO_API_BASE_URL=https://api.coingecko.com/api/v3

# 安全配置
HELMET_CSP_ENABLED=true
TRUST_PROXY=false
```

### 开发模式

```bash
# 启动开发服务器 (热重载)
npm run dev

# 运行测试
npm test

# 监听测试文件变化
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage

# 代码检查
npm run lint

# 自动修复代码问题
npm run lint:fix

# 代码格式化
npm run format
```

### 生产部署

**使用 PM2 部署**:
```bash
# 1. 全局安装 PM2
npm install -g pm2

# 2. 构建生产版本
npm run build

# 3. 启动应用
pm2 start dist/app.js --name "chainmix-backend"

# 4. 设置开机自启
pm2 startup
pm2 save
```

**使用 Docker 部署**:
```dockerfile
# Dockerfile 示例
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist
COPY .env ./

EXPOSE 3000

CMD ["node", "dist/app.js"]
```

### 健康检查

```bash
# 检查服务状态
curl http://localhost:3000/health

# 检查 API 文档
curl http://localhost:3000/api

# 测试新闻端点
curl "http://localhost:3000/api/news?limit=5"

# 测试价格端点
curl "http://localhost:3000/api/prices/top?limit=10"
```

---

## 最佳实践

### 代码质量

**TypeScript 最佳实践**:
```typescript
// 1. 严格的类型定义
interface StrictApiResponse<T> {
  readonly success: boolean;
  readonly data: T;
  readonly message?: string;
  readonly timestamp: string;
}

// 2. 使用泛型增强复用性
class BaseService<T> {
  protected async fetchData<R>(url: string): Promise<R> {
    // 通用数据获取逻辑
  }
}

// 3. 利用工具类型
type NewsApiFields = keyof NewsApiArticle;
type RequiredNewsFields = Required<Pick<NewsArticle, 'id' | 'title' | 'publishedAt'>>;
```

**错误处理最佳实践**:
```typescript
// 1. 分层错误处理
try {
  const result = await externalApiCall();
  return this.transformData(result);
} catch (error) {
  if (error.response?.status === 429) {
    throw new TooManyRequestsError('Rate limit exceeded');
  }
  if (error.response?.status >= 500) {
    throw new BadGatewayError('External service unavailable');
  }
  throw new ServiceUnavailableError('Service temporarily unavailable');
}

// 2. 结构化错误日志
logger.error('External API error', {
  service: 'NewsAPI',
  endpoint: '/everything',
  statusCode: error.response?.status,
  message: error.message,
  requestId: req.headers['x-request-id'],
});
```

### 性能优化实践

**缓存策略优化**:
```typescript
// 1. 智能缓存失效
class SmartCache {
  async invalidatePattern(pattern: string): Promise<void> {
    const keys = this.cache.keys().filter(key => key.includes(pattern));
    keys.forEach(key => this.cache.del(key));
  }

  // 2. 预热重要数据
  async warmupCache(): Promise<void> {
    await Promise.all([
      this.newsService.getCategories(),
      this.pricesService.getTopCryptocurrencies(50),
      this.newsService.getTrendingArticles(20),
    ]);
  }
}
```

**异步处理优化**:
```typescript
// 1. 并发请求处理
const [articles, prices, categories] = await Promise.allSettled([
  this.newsService.getArticles(1, 20),
  this.pricesService.getTopCryptocurrencies(10),
  this.newsService.getCategories(),
]);

// 2. 请求去重
class RequestDeduplication {
  private pendingRequests = new Map<string, Promise<any>>();

  async request<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key);
    }

    const promise = fetcher();
    this.pendingRequests.set(key, promise);
    
    try {
      const result = await promise;
      return result;
    } finally {
      this.pendingRequests.delete(key);
    }
  }
}
```

### 监控和日志实践

**结构化日志**:
```typescript
// 1. 业务事件日志
logger.info('News article fetched', {
  articleId: article.id,
  category: article.category.slug,
  source: article.source.name,
  readTime: article.readTime,
  userId: req.user?.id,
  sessionId: req.sessionId,
  requestId: req.headers['x-request-id'],
});

// 2. 性能监控日志
logger.info('Performance metric', {
  operation: 'fetch_news_articles',
  duration: Date.now() - startTime,
  cacheHit: cachedData ? true : false,
  itemsReturned: articles.length,
  memoryUsage: process.memoryUsage(),
});
```

**健康检查端点**:
```typescript
app.get('/health', async (req, res) => {
  const healthStatus = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version,
    services: {
      cache: cacheManager.getStats(),
      memory: process.memoryUsage(),
      externalApis: await this.checkExternalServices(),
    },
  };

  res.status(200).json(healthStatus);
});
```

### 安全实践

**API 密钥管理**:
```typescript
// 1. 环境变量验证
const requiredEnvVars = ['NEWS_API_KEY', 'COINGECKO_API_KEY'];
requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    logger.error(`Missing required environment variable: ${envVar}`);
    process.exit(1);
  }
});

// 2. 密钥轮换支持
class ApiKeyManager {
  private currentKeyIndex = 0;
  private apiKeys: string[];

  constructor(keys: string[]) {
    this.apiKeys = keys.filter(Boolean);
  }

  getCurrentKey(): string {
    return this.apiKeys[this.currentKeyIndex];
  }

  rotateKey(): void {
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
  }
}
```

**请求验证增强**:
```typescript
// 1. 自定义验证规则
const NewsQuerySchema = z.object({
  page: z.coerce.number().min(1).max(1000),
  limit: z.coerce.number().min(1).max(100),
  category: z.string().refine(
    val => VALID_CATEGORIES.includes(val),
    { message: 'Invalid category' }
  ).optional(),
});

// 2. 输入清理
const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>\"']/g, '') // 移除潜在的 XSS 字符
    .substring(0, 1000); // 限制长度
};
```

---

## 总结

ChainMix 后端服务采用现代化的架构设计和最佳实践，为区块链资讯移动应用提供高性能、高可用的 API 服务。

### 技术亮点
- ✅ **现代化技术栈**: Node.js 18+ + Express + TypeScript
- ✅ **企业级安全**: 完整的安全中间件和验证机制
- ✅ **高性能缓存**: 智能缓存策略和内存管理
- ✅ **可观测性**: 结构化日志和监控体系
- ✅ **测试驱动**: 完整的单元测试和集成测试
- ✅ **代码质量**: ESLint + Prettier + 严格的 TypeScript

### 业务特色
- 📰 **实时新闻**: 集成权威新闻源，提供实时区块链资讯
- 💰 **价格数据**: 对接 CoinGecko，提供准确的加密货币价格
- 🔍 **智能搜索**: 跨内容类型的全文搜索和智能推荐
- ⚡ **高性能**: 多层缓存和请求优化，确保快速响应
- 🔒 **安全可靠**: 完善的安全机制和错误处理

### 可扩展性
项目架构支持水平扩展和功能扩展，预留了数据库集成、消息队列、微服务拆分等扩展点，为未来业务增长提供了坚实的技术基础。

---

*文档版本: 1.0.0*  
*最后更新: 2025-07-10*