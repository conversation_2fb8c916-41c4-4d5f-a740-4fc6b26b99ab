# ChainMix 增强型数据抓取系统

本文档介绍了 ChainMix 项目中新实现的增强型数据抓取系统，它支持断点续抓、增量抓取、强制全量抓取等高级功能。

## 系统概述

增强型数据抓取系统基于现有的 draft 目录中的 API 源码示例，为 9 个主要区块链新闻数据源提供了完整的抓取解决方案：

### 支持的数据源

1. **ChainCatcher** - 链捕手
   - 文章 (article)
   - 快讯 (flash)

2. **ChainFeeds** - 链闻
   - 精选资讯 (feed)
   - 快讯 (flash)

3. **Followin** - 关注链
   - 新闻 (news)

4. **ForesightNews** - 远见新闻
   - 新闻 (news)
   - 订阅源 (feed)

5. **Odaily** - 星球日报
   - 快讯 (flash)
   - 文章 (post)

6. **TheBlockBeats** - 律动 BlockBeats
   - 快讯 (flash)

7. **TrendX** - 趋势交易
   - 新闻 (news)

8. **PANews** - PA新闻实验室
   - 快讯 (flash)

## 核心功能

### 1. 断点续抓
- 任务意外中断时，可以从上次停止的位置继续抓取
- 保存抓取进度，包括当前页数、已处理项目数等
- 支持手动暂停和恢复任务

### 2. 增量抓取
- 自动检测最新数据的时间戳
- 只抓取比上次抓取更新的内容
- 避免重复抓取，提高效率

### 3. 强制全量抓取
- 忽略已有数据，重新抓取所有历史信息
- 适用于数据重建或初次抓取场景
- 支持设置最大抓取页数限制

### 4. 智能数据解析
- 为每个 API 源配置专门的解析器
- 自动处理压缩数据（如 ForesightNews 的 zlib 压缩）
- 统一的数据格式输出

### 5. 任务管理
- 实时任务状态监控
- 支持并发抓取多个数据源
- 错误处理和重试机制

## 数据库设计

### 数据项集合 (scraped_items)
```javascript
{
  id: "源名称-原始ID",           // 唯一标识
  title: "标题",
  content: "内容摘要",
  url: "原文链接",
  publishTime: Date,            // 发布时间
  source: "数据源名称",
  dataType: "数据类型",
  tags: ["标签1", "标签2"],     // 标签数组
  author: "作者",
  originalData: {},             // 原始API返回数据
  metadata: {                   // 额外元数据
    importance: 1,
    readCount: 100,
    likeCount: 50,
    shareCount: 20
  }
}
```

### 抓取任务集合 (scraping_tasks)
```javascript
{
  source: "数据源",
  dataType: "数据类型",
  status: "pending|running|completed|failed|paused",
  startTime: Date,
  endTime: Date,
  lastPageProcessed: 10,        // 最后处理的页数
  totalPages: 50,
  totalItems: 1000,
  processedItems: 500,          // 已处理项目数
  failedItems: 5,               // 失败项目数
  errorMessage: "错误信息",
  resumeData: {},               // 断点续传数据
  forceFullScrape: false,       // 是否强制全量抓取
  createdAt: Date,
  updatedAt: Date
}
```

### 数据源配置集合 (source_configs)
```javascript
{
  source: "数据源名称",
  endpoints: {
    "数据类型": {
      url: "API地址",
      method: "GET|POST",
      headers: {},
      params: {},
      body: {},
      parser: "解析器名称",
      paging: {
        pageParam: "页数参数名",
        sizeParam: "页大小参数名",
        defaultSize: 20,
        maxPages: 100
      }
    }
  }
}
```

## API 接口

### 基础操作

#### 初始化配置
```http
POST /api/enhanced-scraper/init
```
初始化数据源配置和数据库索引。

#### 开始抓取
```http
POST /api/enhanced-scraper/scrape
Content-Type: application/json

{
  "source": "chaincatcher",
  "dataType": "article",
  "forceFullScrape": false,
  "maxPages": 10
}
```

#### 批量抓取所有数据源
```http
POST /api/enhanced-scraper/scrape-all
Content-Type: application/json

{
  "forceFullScrape": false,
  "maxPages": 5
}
```

### 任务管理

#### 获取所有任务状态
```http
GET /api/enhanced-scraper/tasks
```

#### 获取特定任务状态
```http
GET /api/enhanced-scraper/tasks?source=chaincatcher&dataType=article
```

#### 暂停任务
```http
POST /api/enhanced-scraper/tasks/{taskId}/pause
```

#### 恢复任务
```http
POST /api/enhanced-scraper/tasks/{taskId}/resume
```

### 数据查询

#### 获取抓取数据
```http
GET /api/enhanced-scraper/data?source=chaincatcher&dataType=article&page=1&limit=20
```

#### 搜索数据
```http
GET /api/enhanced-scraper/search?q=比特币&source=chaincatcher&page=1&limit=20
```

#### 获取统计信息
```http
GET /api/enhanced-scraper/stats
```

### 维护操作

#### 清理旧数据
```http
POST /api/enhanced-scraper/cleanup
Content-Type: application/json

{
  "days": 30
}
```

#### 健康检查
```http
GET /api/enhanced-scraper/health
```

## 命令行工具

### 安装和使用

```bash
# 编译 TypeScript
npm run build

# 使用命令行工具
node dist/scraper/enhanced-cli.js --help
```

### 常用命令

#### 初始化
```bash
node dist/scraper/enhanced-cli.js init
```

#### 抓取特定数据源
```bash
# 抓取 chaincatcher 的所有数据类型
node dist/scraper/enhanced-cli.js scrape chaincatcher

# 抓取 chaincatcher 的 article 数据
node dist/scraper/enhanced-cli.js scrape chaincatcher article

# 强制全量抓取
node dist/scraper/enhanced-cli.js scrape chaincatcher article --force

# 限制抓取页数
node dist/scraper/enhanced-cli.js scrape chaincatcher article --max-pages 5
```

#### 批量抓取
```bash
# 抓取所有数据源
node dist/scraper/enhanced-cli.js scrape all

# 强制全量抓取所有数据源
node dist/scraper/enhanced-cli.js scrape all --force
```

#### 查看状态
```bash
# 查看所有任务状态
node dist/scraper/enhanced-cli.js status

# 查看特定任务状态
node dist/scraper/enhanced-cli.js status chaincatcher article
```

#### 任务管理
```bash
# 暂停任务
node dist/scraper/enhanced-cli.js pause <taskId>

# 恢复任务
node dist/scraper/enhanced-cli.js resume <taskId>
```

#### 统计和维护
```bash
# 查看统计信息
node dist/scraper/enhanced-cli.js stats

# 清理30天前的数据
node dist/scraper/enhanced-cli.js cleanup --days 30
```

#### 交互模式
```bash
# 进入交互式命令行
node dist/scraper/enhanced-cli.js interactive
```

## 配置说明

### 环境变量

```bash
# MongoDB 连接
MONGODB_URL=****************************************************************************************
MONGODB_DB_NAME=chainmix

# 日志级别
LOG_LEVEL=info

# Node.js 环境
NODE_ENV=production
```

### 抓取配置

每个数据源都有详细的配置，包括：
- API 端点 URL
- 请求方法和参数
- 请求头设置
- 分页参数
- 数据解析器

## 部署和监控

### Docker 部署

```dockerfile
# 在现有的 Dockerfile 中添加
RUN npm run build

# 启动增强型抓取器
CMD ["node", "dist/scraper/enhanced-cli.js", "interactive"]
```

### 定时任务

使用 cron 或其他调度器定期执行增量抓取：

```bash
# 每小时增量抓取所有数据源
0 * * * * cd /path/to/chainmix/server && node dist/scraper/enhanced-cli.js scrape all

# 每天凌晨2点清理30天前的数据
0 2 * * * cd /path/to/chainmix/server && node dist/scraper/enhanced-cli.js cleanup --days 30
```

### 监控指标

系统提供详细的统计信息：
- 各数据源的数据量和最新抓取时间
- 任务执行状态和成功率
- 错误日志和重试次数
- 数据库存储使用情况

## 故障排除

### 常见问题

1. **抓取失败**
   - 检查网络连接
   - 验证API端点是否有变化
   - 查看错误日志

2. **数据重复**
   - 检查唯一ID生成逻辑
   - 验证数据库索引
   - 使用强制全量抓取重建数据

3. **任务卡住**
   - 检查任务状态
   - 手动暂停后重启
   - 查看数据库连接

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 实时监控抓取进度
node dist/scraper/enhanced-cli.js status
```

## 性能优化

### 抓取策略

1. **并发控制**：避免同时抓取过多数据源
2. **请求频率**：在请求间添加适当延迟
3. **数据去重**：使用唯一ID避免重复存储
4. **索引优化**：为常用查询字段创建索引

### 存储优化

1. **分页查询**：大量数据使用分页避免内存溢出
2. **定期清理**：删除过期数据释放存储空间
3. **压缩存储**：对大文本内容进行压缩
4. **冷热分离**：将历史数据迁移到冷存储

## 扩展开发

### 添加新数据源

1. 在 `draft` 目录创建 API 示例文件
2. 在 `enhanced-scraper.ts` 中添加数据源配置
3. 实现对应的数据解析器
4. 更新 CLI 工具和 API 路由

### 自定义解析器

```typescript
// 在 parseResponseData 方法中添加新的 case
case 'newSourceParser':
  if (data.results) {
    for (const item of data.results) {
      items.push({
        id: `${source}-${item.id}`,
        title: item.title,
        content: item.summary,
        url: item.link,
        publishTime: new Date(item.publishedAt),
        source,
        dataType,
        originalData: item
      });
    }
  }
  break;
```

## 安全考虑

### 访问控制

- API 端点添加认证和授权
- 限制敏感操作的访问权限
- 使用 HTTPS 加密传输

### 数据保护

- 对原始 API 密钥进行加密存储
- 定期轮换访问凭证
- 监控异常访问模式

### 合规性

- 遵守数据源的使用条款
- 控制抓取频率避免被封禁
- 尊重 robots.txt 和 rate limiting

---

本增强型数据抓取系统为 ChainMix 提供了可靠、高效、可扩展的数据收集解决方案，支持断点续抓和增量更新，确保数据的完整性和实时性。