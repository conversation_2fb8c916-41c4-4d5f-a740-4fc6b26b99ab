# ChainMix Accurate Scraper Integration Guide

## Overview

The ChainMix project has been successfully integrated with a comprehensive, accurate API scraper system that implements precise scraping for 9 blockchain news sources based on their actual API specifications. This replaces the previous enhanced scraper with a more accurate and reliable system.

## New Scraper Architecture

### Core Components

1. **AccurateDataScraper** (`/server/src/scraper/accurate-scraper.ts`)
   - Main scraper class with precise API configurations
   - Supports breakpoint resume and incremental scraping
   - Handles compressed data (ForesightNews)
   - Implements all 9 data sources with exact headers, methods, and parameters

2. **Accurate CLI Tool** (`/server/src/scraper/accurate-cli.ts`)
   - Command-line interface for interactive scraping
   - Comprehensive endpoint listing and task management
   - Real-time progress monitoring

3. **API Routes** (`/server/src/routes/accurate-scraper.ts`)
   - RESTful endpoints for scraper management
   - Complete CRUD operations for scraping tasks
   - Health checking and statistics

### Supported Data Sources

The accurate scraper supports **9 major blockchain news sources** with **37 total endpoints**:

1. **ChainCatcher** (2 endpoints)
   - `article` - 文章内容
   - `flash` - 快讯消息

2. **ChainFeeds** (4 endpoints)
   - `feed` - 精选内容
   - `flash` - 快讯
   - `topic` - 主题聚合
   - `subject` - 原创内容

3. **Followin** (1 endpoint)
   - `flash` - 推荐新闻

4. **Odaily** (5 endpoints)
   - `depth` - 深度分析
   - `flash` - 快讯消息
   - `post` - 普通文章
   - `price` - 加密货币价格
   - `tweet` - 用户观点和推文

5. **TechFlow** (1 endpoint)
   - `flash` - 快讯消息

6. **TrendX** (3 endpoints)
   - `financing` - 项目融资数据
   - `news` - 新闻聚合
   - `tweet` - Twitter相关内容

7. **TheBlockBeats** (2 endpoints)
   - `flash` - 律动BlockBeats快讯
   - `finance` - 融资信息

8. **PANewsLab** (1 endpoint)
   - `flash` - 快讯消息

9. **ForesightNews** (10 endpoints)
   - `dayNews` - 每日快讯汇总
   - `news` - 新闻
   - `feed` - 订阅内容
   - `event` - 区块链事件
   - `topic` - 热门专题
   - `column` - 专栏文章
   - `article` - 专栏具体文章
   - `tools` - 区块链工具集
   - `fundraising` - 融资信息
   - `calendars` - 事件日历

## API Endpoints

The accurate scraper system provides the following REST API endpoints:

### Initialization
- `POST /api/accurate-scraper/init` - Initialize scraper configurations

### Endpoint Management
- `GET /api/accurate-scraper/endpoints` - List all available API endpoints

### Scraping Operations
- `POST /api/accurate-scraper/scrape` - Start scraping specific source/type
- `POST /api/accurate-scraper/scrape-source/:source` - Scrape all endpoints for a source
- `POST /api/accurate-scraper/scrape-all` - Scrape all sources and endpoints

### Task Management
- `GET /api/accurate-scraper/tasks` - Get task status and history
- `POST /api/accurate-scraper/tasks/:taskId/pause` - Pause a running task
- `POST /api/accurate-scraper/tasks/:taskId/resume` - Resume a paused task

### Data Access
- `GET /api/accurate-scraper/data` - Query scraped data with filters
- `GET /api/accurate-scraper/search` - Search scraped content
- `GET /api/accurate-scraper/stats` - Get scraping statistics

### Maintenance
- `POST /api/accurate-scraper/cleanup` - Clean up old data
- `GET /api/accurate-scraper/health` - Health check endpoint

## Key Features

### 1. Breakpoint Resume Capability
- Tasks automatically track progress (page numbers, processed items)
- Failed tasks can be resumed from the last successful point
- Prevents data loss during interruptions

### 2. Incremental Scraping
- Only fetches new data since the last successful scrape
- Configurable `forceFullScrape` option for full historical data
- Optimizes bandwidth and processing time

### 3. Precise API Implementation
- Exact headers, methods, and body formats from draft API specifications
- Special handling for compressed data (ForesightNews uses zlib compression)
- Proper pagination handling for different API patterns

### 4. Comprehensive Task Management
- Task status tracking: `pending`, `running`, `completed`, `failed`, `paused`
- Detailed progress monitoring (processed items, failed items, current page)
- Error logging and retry mechanisms

### 5. MongoDB Integration
- Three main collections:
  - `scraped_items` - Stores all scraped data
  - `scraping_tasks` - Task management and progress tracking
  - `source_configs` - API endpoint configurations
- Proper indexing for optimal performance

## Usage Examples

### Initialize the System
```bash
curl -X POST http://localhost:3001/api/accurate-scraper/init
```

### List Available Endpoints
```bash
curl http://localhost:3001/api/accurate-scraper/endpoints
```

### Start Scraping a Specific Source
```bash
curl -X POST http://localhost:3001/api/accurate-scraper/scrape \
  -H "Content-Type: application/json" \
  -d '{"source": "chaincatcher", "dataType": "article"}'
```

### Start Incremental Scraping
```bash
curl -X POST http://localhost:3001/api/accurate-scraper/scrape \
  -H "Content-Type: application/json" \
  -d '{"source": "foresightnews", "dataType": "news", "forceFullScrape": false}'
```

### Scrape All Endpoints for a Source
```bash
curl -X POST http://localhost:3001/api/accurate-scraper/scrape-source/odaily \
  -H "Content-Type: application/json" \
  -d '{"maxPages": 5}'
```

### Check Task Status
```bash
curl http://localhost:3001/api/accurate-scraper/tasks
```

### Search Scraped Data
```bash
curl "http://localhost:3001/api/accurate-scraper/search?q=bitcoin&source=chaincatcher&limit=10"
```

### Get Statistics
```bash
curl http://localhost:3001/api/accurate-scraper/stats
```

## CLI Usage

The accurate scraper also provides a comprehensive command-line interface:

```bash
# Navigate to server directory
cd server

# Build the project
npm run build

# Initialize configurations
node dist/scraper/accurate-cli.js init

# List all available endpoints
node dist/scraper/accurate-cli.js list-endpoints

# Scrape specific source and type
node dist/scraper/accurate-cli.js scrape chaincatcher article

# Scrape all endpoints for a source
node dist/scraper/accurate-cli.js scrape foresightnews

# Scrape all sources (use with caution)
node dist/scraper/accurate-cli.js scrape all

# Force full scraping (ignore incremental)
node dist/scraper/accurate-cli.js scrape odaily flash --force

# Check task status
node dist/scraper/accurate-cli.js status

# View detailed statistics
node dist/scraper/accurate-cli.js stats

# Query scraped data
node dist/scraper/accurate-cli.js query --source chaincatcher --limit 5

# Interactive mode
node dist/scraper/accurate-cli.js interactive
```

## Database Schema

### scraped_items Collection
```javascript
{
  _id: ObjectId,
  id: String,                    // Unique identifier: "source-originalId"
  title: String,                 // Article/news title
  content: String,               // Article content or description
  url: String,                   // Source URL
  publishTime: Date,             // Publication timestamp
  source: String,                // Data source name
  dataType: String,              // Endpoint type
  tags: [String],                // Associated tags
  author: String,                // Author name (if available)
  metadata: {                    // Additional metadata
    importance: Number,
    readCount: Number,
    likeCount: Number,
    shareCount: Number
  },
  originalData: Object,          // Original API response
  updatedAt: Date
}
```

### scraping_tasks Collection
```javascript
{
  _id: ObjectId,
  source: String,                // Data source name
  dataType: String,              // Endpoint type
  status: String,                // pending|running|completed|failed|paused
  startTime: Date,               // Task start time
  endTime: Date,                 // Task completion time
  lastPageProcessed: Number,     // Last successfully processed page
  totalPages: Number,            // Total pages discovered
  totalItems: Number,            // Total items processed
  processedItems: Number,        // Successfully processed items
  failedItems: Number,           // Failed item count
  errorMessage: String,          // Last error message
  resumeData: Object,            // Resume state data
  forceFullScrape: Boolean,      // Full vs incremental scraping
  createdAt: Date,
  updatedAt: Date
}
```

### source_configs Collection
```javascript
{
  _id: ObjectId,
  source: String,                // Data source name
  endpoints: {                   // Endpoint configurations
    [endpointName]: {
      url: String,               // API endpoint URL
      method: String,            // HTTP method (GET|POST)
      headers: Object,           // Request headers
      params: Object,            // URL parameters (GET)
      body: Object,              // Request body (POST)
      bodyType: String,          // json|form
      parser: String,            // Parser function name
      paging: {                  // Pagination configuration
        pageParam: String,       // Page parameter name
        sizeParam: String,       // Page size parameter name
        defaultSize: Number,     // Default page size
        maxPages: Number         // Maximum pages (optional)
      }
    }
  }
}
```

## Integration Status

✅ **Completed Integration Tasks:**
1. Analyzed existing draft API sources code structure
2. Read and understood each API source implementation
3. Designed MongoDB schemas for each API source
4. Implemented base scraper class with resume/incremental features
5. Implemented individual scrapers for each API source
6. Removed duplicate code and integrated with existing project
7. Fixed TypeScript compilation errors in route handlers
8. Updated main application to use accurate scraper routes

## Configuration

The accurate scraper uses the following environment variables:

```bash
MONGODB_URL=****************************************************************************************
MONGODB_DB_NAME=chainmix
```

## Performance Considerations

1. **Rate Limiting**: Built-in delays between requests to avoid overwhelming source APIs
2. **Batch Processing**: Efficient bulk operations for database writes
3. **Error Handling**: Comprehensive error catching and retry mechanisms
4. **Memory Management**: Streaming data processing for large datasets
5. **Index Optimization**: Proper database indexing for fast queries

## Monitoring and Maintenance

- Use `/api/accurate-scraper/health` for system health monitoring
- Check `/api/accurate-scraper/stats` for performance metrics
- Regular cleanup with `/api/accurate-scraper/cleanup` to manage storage
- Monitor task status via `/api/accurate-scraper/tasks` for failed jobs

This accurate scraper system provides a robust, maintainable, and scalable solution for blockchain news data collection that precisely follows each source's API specifications.