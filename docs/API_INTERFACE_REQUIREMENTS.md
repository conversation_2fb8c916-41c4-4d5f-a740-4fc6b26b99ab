# API接口需求文档

## 概述

本文档定义了移动端区块链资讯应用首页各模块所需的API接口规范，按优先级排序：**精选文章 > 热门推特 > 热门资讯 > 其他模块**。

## 当前状态分析

### ✅ 已完成的接口

1. **精选文章模块**
   - 接口：`GET /api/news`
   - 状态：✅ 完全集成
   - 缓存：✅ 已实现
   - 错误处理：✅ 已实现

2. **热门资讯模块**
   - 接口：`GET /api/news/trending`
   - 状态：✅ 完全集成
   - 缓存：✅ 已实现
   - 错误处理：✅ 已实现

### ⚠️ 需要优化的接口

3. **推特观点模块**
   - 接口：`GET /api/hot-tweets`
   - 状态：⚠️ 部分集成，需要优化数据结构
   - 问题：API失败时降级到模拟数据

## 接口规范定义

### 1. 精选文章接口（优先级：高）

**接口URL：** `GET /api/news`

**请求参数：**
```typescript
interface FeaturedArticlesQuery {
  page?: number;        // 页码，默认1
  limit?: number;       // 每页数量，默认20
  category?: string;    // 分类筛选，可选
  featured?: boolean;   // 是否只返回精选文章，默认false
}
```

**响应数据结构：**
```typescript
interface FeaturedArticlesResponse {
  success: boolean;
  data: {
    items: NewsArticle[];
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
  message: string;
}

interface NewsArticle {
  id: string;
  title: string;
  summary: string;
  content?: string;
  imageUrl?: string;
  publishedAt: string;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  source: {
    id: string;
    name: string;
    url: string;
  };
  tags: string[];
  readTime: number;
  isFeatured: boolean;
}
```

**错误处理：**
- 网络错误：返回缓存数据
- 服务器错误：返回模拟数据（开发环境）
- 数据为空：返回空数组

### 2. 热门推特接口（优先级：高）

**接口URL：** `GET /api/hot-tweets`

**请求参数：**
```typescript
interface HotTweetsQuery {
  limit?: number;       // 返回数量，默认10，最大50
  timeRange?: string;   // 时间范围：1h, 24h, 7d，默认24h
}
```

**响应数据结构：**
```typescript
interface HotTweetsResponse {
  success: boolean;
  data: TwitterPost[];
  message: string;
}

interface TwitterPost {
  id: string;
  author: string;
  handle: string;
  content: string;
  timestamp: string;
  likes: number;
  retweets: number;
  replies: number;
  verified: boolean;
  avatarUrl?: string;
  tweetUrl: string;
  engagement: {
    score: number;      // 热度评分
    trend: 'up' | 'down' | 'stable';
  };
}
```

**错误处理：**
- API失败：返回模拟数据
- 数据为空：返回空数组
- 缓存策略：5分钟缓存

### 3. 热门资讯接口（优先级：中）

**接口URL：** `GET /api/news/trending`

**请求参数：**
```typescript
interface TrendingNewsQuery {
  limit?: number;       // 返回数量，默认10，最大50
  timeRange?: string;   // 时间范围：1h, 6h, 24h，默认24h
}
```

**响应数据结构：**
```typescript
interface TrendingNewsResponse {
  success: boolean;
  data: TrendingArticle[];
  message: string;
}

interface TrendingArticle extends NewsArticle {
  trending: {
    rank: number;       // 排名
    score: number;      // 热度评分
    change: number;     // 排名变化
    views: number;      // 浏览量
    engagement: number; // 互动量
  };
}
```

### 4. 其他支持接口

#### 4.1 分类接口
**接口URL：** `GET /api/categories`

#### 4.2 搜索接口
**接口URL：** `GET /api/search`

#### 4.3 价格数据接口
**接口URL：** `GET /api/prices`

## 缓存策略

| 接口 | 缓存时间 | 缓存键 |
|------|----------|--------|
| 精选文章 | 5分钟 | `articles_{page}_{limit}_{category}` |
| 热门推特 | 5分钟 | `hot_tweets_{limit}_{timeRange}` |
| 热门资讯 | 3分钟 | `trending_{limit}_{timeRange}` |

## 错误处理机制

### 1. 网络错误
- 优先返回缓存数据
- 缓存不存在时返回模拟数据（开发环境）
- 生产环境显示友好错误提示

### 2. 服务器错误
- 5xx错误：返回缓存数据
- 4xx错误：显示具体错误信息
- 超时错误：重试机制（最多3次）

### 3. 数据验证错误
- 数据格式不正确：过滤无效数据
- 必填字段缺失：使用默认值
- 图片链接无效：使用占位符

## 性能优化建议

### 1. 数据预加载
- 首页进入时并行加载所有模块数据
- 使用Promise.all提高加载效率

### 2. 缓存优化
- 实现多级缓存：内存缓存 + 本地存储
- 缓存失效策略：时间过期 + 手动刷新

### 3. 数据压缩
- 启用gzip压缩
- 图片使用WebP格式
- 分页加载减少单次数据量

## 实施计划

### 阶段一：推特接口优化（当前）
1. 优化`/api/hot-tweets`接口数据结构
2. 改进错误处理机制
3. 实现更好的缓存策略

### 阶段二：数据质量提升
1. 增加数据验证和清洗
2. 优化图片处理和占位符
3. 改进时间格式化

### 阶段三：性能优化
1. 实现数据预加载
2. 优化缓存策略
3. 添加离线支持

## 监控和日志

### 1. API调用监控
- 请求成功率
- 响应时间
- 错误率统计

### 2. 缓存命中率
- 缓存使用情况
- 缓存失效原因
- 缓存性能指标

### 3. 用户体验指标
- 页面加载时间
- 数据刷新频率
- 错误发生频率
