# ChainMix数据抓取系统使用指南

## 快速开始

### 1. 启动MongoDB

```bash
# 启动MongoDB Docker容器
cd /home/<USER>/conan-work/chainmix
./scripts/mongo.sh start

# 查看MongoDB状态
./scripts/mongo.sh status

# 进入MongoDB Shell
./scripts/mongo.sh shell
```

### 2. 初始化API定义

```bash
cd server
npm run scraper:init
```

这会扫描`server/src/draft`目录，提取所有API定义并保存到MongoDB。

### 3. 开始数据抓取

```bash
# 抓取所有数据源
npm run scraper:scrape-all

# 抓取特定数据源
npm run scraper scrape-source chaincatcher

# 抓取特定数据类型
npm run scraper scrape-type flash_news

# 查看统计信息
npm run scraper:stats
```

### 4. 使用API服务

启动后端服务：
```bash
npm run dev
```

访问以下端点：

#### 获取API定义
```bash
GET http://localhost:3001/api/scraper/api-definitions
GET http://localhost:3001/api/scraper/api-definitions?source=chaincatcher
```

#### 获取抓取数据
```bash
GET http://localhost:3001/api/scraper/scraped-data
GET http://localhost:3001/api/scraper/scraped-data?source=chainfeeds&success_only=true
GET http://localhost:3001/api/scraper/scraped-data/foresightnews
```

#### 搜索数据
```bash
GET http://localhost:3001/api/scraper/search?keyword=比特币&source=chaincatcher
```

#### 获取统计信息
```bash
GET http://localhost:3001/api/scraper/stats
GET http://localhost:3001/api/scraper/sources
GET http://localhost:3001/api/scraper/data-types
```

#### 触发抓取任务
```bash
POST http://localhost:3001/api/scraper/scrape/all
POST http://localhost:3001/api/scraper/scrape/source/chainfeeds
POST http://localhost:3001/api/scraper/scrape/type/flash_news
```

## 系统架构

### 数据源支持
- **ChainCatcher** (链捕手) - 文章和快讯
- **ChainFeeds** (链闻聚合) - 资讯、快讯、主题、原创文章
- **Followin** (跟新) - 快讯推荐
- **ForesightNews** (前瞻新闻) - 多类型数据(支持压缩数据解码)
- **Odaily** (星球日报) - 快讯、文章、价格、主题
- **TheBlockBeats** (律动) - 快讯和金融数据
- **TrendX** (趋势) - 新闻、融资、推特
- **PANewsLab** (PA新闻) - 快讯

### 数据类型
- `flash_news` - 快讯
- `article` - 文章
- `feed` - 订阅源
- `topic` - 主题
- `event` - 事件
- `financing` - 融资信息
- `price` - 价格数据
- `social_media` - 社交媒体

### MongoDB集合
- `api_definitions` - API定义
- `scraped_data` - 抓取的数据

## 命令行工具

### 基本命令
```bash
# 初始化API定义
npm run scraper init

# 抓取所有数据源
npm run scraper scrape-all

# 抓取指定数据源
npm run scraper scrape-source <source>

# 抓取指定数据类型
npm run scraper scrape-type <type>

# 查看API定义
npm run scraper list-apis

# 查看抓取数据
npm run scraper list-data -l 5

# 统计信息
npm run scraper stats

# 清理旧数据(保留30天)
npm run scraper cleanup

# 交互式模式
npm run scraper interactive
```

### 高级用法
```bash
# 按数据源过滤API定义
npm run scraper list-apis -s chainfeeds

# 按数据源和类型过滤数据
npm run scraper list-data -s foresightnews -t flash_news -l 10

# 清理指定天数的数据
npm run scraper cleanup -d 7
```

## 开发指南

### 添加新数据源

1. 在`server/src/draft/`目录下创建新的`.ts`文件
2. 定义fetch调用和响应示例
3. 运行`npm run scraper init`重新初始化API定义
4. 测试数据抓取：`npm run scraper scrape-source <新数据源名>`

### 自定义抓取逻辑

在`ApiExtractor`类中修改：
- `extractFetchCalls()` - 提取fetch调用的逻辑
- `determineDataType()` - 数据类型识别逻辑
- `parseCompressedProperties()` - 特殊数据解析

### 扩展API服务

在`server/src/routes/scraper.ts`中添加新的路由端点。

## 故障排除

### MongoDB连接问题
```bash
# 检查容器状态
./scripts/mongo.sh status

# 查看容器日志
./scripts/mongo.sh logs

# 重启容器
./scripts/mongo.sh restart
```

### 抓取失败
```bash
# 查看抓取统计，检查成功率
npm run scraper stats

# 查看失败的数据记录
npm run scraper list-data | grep "成功: 否"
```

### API服务问题
检查日志输出，确保：
1. MongoDB连接正常
2. API定义已初始化
3. 数据已抓取

## 性能优化

### 数据库索引
MongoDB脚本已自动创建必要索引：
- `api_definitions`: `(source, endpoint)`, `created_at`
- `scraped_data`: `(source, data_type)`, `scraped_at`

### 缓存策略
- API服务自动连接复用
- 支持分页减少内存使用
- 错误处理避免级联失败

### 监控建议
- 定期运行`npm run scraper stats`检查数据质量
- 监控成功率和抓取频率
- 定期清理旧数据释放存储空间