您是一位专业的 Node.js 和 TypeScript 后端开发专家，精通 Express 框架、API 聚合和代理请求处理。您的任务是为一个基于 Node.js + Express + TypeScript 的区块链资讯应用（工程目录在 `server/`）实现一个聚合的区块链信息和快讯接口，位于 `server/src/fetcher`。该应用的前端基于 Expo + React Native，提供新闻列表、推特列表、快讯（参考 `docs/front.md`）。请阅读 `server/src/draft` 中的示例区块链信息源 API，分析前端所需的 API，并在 `server/src/fetcher` 实现聚合接口，支持 `Origin`、`Referer`、`X-Forwarded-For`（随机 IP）、`User-Agent`（随机UA）和代理列表。生成代码和文档，追加到 `docs/fetcher.md`。请按照以下要求完成：

### 任务要求
1. **分析 Draft 源码**：
   - 阅读 `server/src/draft` 中的示例 API，提取：
     - 端点（如 `https://www.chaincatcher.com/pc/content/page`）。
     - 请求头（如 `Origin: https://www.chaincatcher.com/`, `Referer: https://www.chaincatcher.com/`）。
     - 响应格式（如 `{ id: string, title: string }`）。
   - 由于有多个源，分析每个源的相应格式
   - 构建一个可扩展，兼容性强的 相应格式 定义
2. **分析前端 API 需求**：
   - 检查 Expo + React Native 页面，推测所需 API：
     - `GET /api/news`：返回新闻列表
     - `GET /api/feed`：返回feed列表
     - `GET /api/hot-tweets`：返回热门相关推特信息列表
     - `GET /api/flash`：返回快讯
     - `GET /api/prices`：返回价格数据（`{ symbol: string, price: number, timestamp: string }[]`）。
     - `GET /api/search?q=keyword`：搜索新闻。
3. **实现聚合接口**：
   - 在 `server/src/fetcher/fetcher.ts` 实现聚合逻辑，调用多个信息源 API。
   - 支持以下功能：
     - **请求头**：为每个信息源设置正确的 `Origin` 和 `Referer`（基于 `server/src/draft` 示例）。
     - **随机 IP**：在 `X-Forwarded-For` 中生成随机 IP（如 `***********`）。
     - **随机 UA**：在 `User-Agent` 中生成随机 UA。
     - **代理列表**： 支持传入或者配置 http、https、socks5代理。
   - 使用 Axios 和 `http-proxy-middleware` 处理请求或其他先进技术栈。
   - 实现内存缓存（`node-cache`）减少外部 API 调用。
4. **设计后端 API**：
   - 实现 RESTful API，端点包括：
     - `GET /api/news`：聚合新闻。
     - `GET /api/feed`：聚合feed。
     - `GET /api/flash`：聚合快讯。
     - `GET /api/hot-tweets`：聚合热门推特。
     - `GET /api/prices`：聚合价格。
     - `GET /api/search?q=keyword`：搜索新闻。
   - 使用分层架构：路由 → 控制器 → 服务 → 聚合器。
   - 确保响应格式与前端兼容（JSON）。
5. **遵循最佳实践**：
   - **类型安全**：使用 TypeScript 和 Zod 验证输入/输出。
   - **错误处理**：全局错误中间件，返回标准 JSON 错误。
   - **安全性**：使用 Helmet、CORS，限制请求速率。
   - **日志**：使用 Winston 记录请求和错误。
   - **代码规范**：使用 ESLint 和 Prettier。
7. **生成文档**：
   - 追加到 `docs/fetcher.md`，包含：
     - 项目结构：新文件说明。
     - API 端点：URL、方法、请求/响应格式。
     - 运行说明：安装、配置代理、启动。
     - 配置说明：代理列表、API 密钥。
   - 使用 Markdown 格式，包含标题、列表、表格、代码片段。