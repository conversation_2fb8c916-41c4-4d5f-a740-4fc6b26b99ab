仔细阅读项目源码 @server/src/draft/ 里边有获取数据源的示例逻辑。
fetch请求包括的请求方法、url以及相应参数，你应该从中推导出分页相关参数或者时间戳等参数。
response就是返回示例，为了简化，大部分例子中只有list一项。
该源码包括了数据源： 
- chaincatcher
- chainfeeds
- followin
- odaily
- panewslab
- techflow
- theblockbeats

其中 chainfeeds 包括两个API：
- getFeed: 获取订阅源和 
- getTopic: 获取主题新闻

foresightnews 包括三个API：
- dayNews: flash 快讯
- news: 新闻 
- feed: 订阅源

每个文件，都有相关注释，这个API是什么类型，比如：新闻，flash快讯，主题、订阅流等。
请分析这些 API 源，综合 @server/src/fetcher/ 逻辑，帮我实现：
- 定时，比如间隔5分钟（可配置）。从这些数据源收集数据，不同类型数据聚合在一起，过滤重复内容
- 为前端提供这些收集到的数据
