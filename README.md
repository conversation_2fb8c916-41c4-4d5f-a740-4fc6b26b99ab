# ChainMix - 智能区块链资讯平台

一个基于 React Native + Expo 技术栈开发的跨平台移动应用，融合AI智能分析，为用户提供最新的区块链和加密货币资讯。ChainMix 意为"链接混合"，致力于将分散的区块链信息智能整合，为用户提供个性化的资讯体验。

## 🚀 技术栈

- **框架**: React Native + Expo SDK 53
- **语言**: TypeScript
- **UI 组件库**: 自定义组件系统（基于现代设计原则）
- **导航**: React Navigation v6
- **状态管理**: Zustand + 持久化存储
- **图标**: Expo Vector Icons
- **存储**: AsyncStorage + 智能缓存系统
- **性能优化**: 组件懒加载、列表虚拟化、内存缓存

## 📱 功能特性

### 核心功能
- **首页**: 展示最新区块链新闻和热门资讯
- **分类浏览**: 按类别（DeFi、NFT、加密货币等）浏览内容
- **文章详情**: 完整的文章内容展示
- **个人中心**: 用户设置和收藏功能

### 用户体验
- **深色/浅色主题**: 支持主题切换
- **响应式设计**: 适配不同屏幕尺寸
- **流畅动画**: 优化的过渡效果
- **加载状态**: 完善的加载和错误处理
- **收藏功能**: 本地收藏文章管理

### 新闻分类
- 🔥 DeFi (去中心化金融)
- 🎨 NFT (非同质化代币)
- 💰 加密货币
- ⚙️ 区块链技术
- 📋 监管政策
- 📊 市场分析

## 🏗️ 项目结构

```
chainmix/
├── src/
│   ├── components/          # 可复用组件
│   │   ├── common/         # 通用组件
│   │   └── ui/             # UI 组件
│   ├── screens/            # 页面组件
│   │   ├── Home/           # 首页
│   │   ├── Categories/     # 分类页面
│   │   ├── Details/        # 详情页面
│   │   └── Profile/        # 个人中心
│   ├── navigation/         # 导航配置
│   ├── store/              # 状态管理
│   ├── services/           # API 服务
│   ├── types/              # TypeScript 类型定义
│   ├── constants/          # 常量配置
│   └── utils/              # 工具函数
├── assets/                 # 静态资源
├── tamagui.config.ts       # Tamagui 配置
└── App.tsx                 # 应用入口
```

## 🛠️ 开发环境设置

### 前置要求
- Node.js (v16 或更高版本)
- npm 或 yarn
- Expo CLI
- iOS Simulator (macOS) 或 Android Emulator

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd chainmix
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm start
   ```

4. **运行应用**
   ```bash
   # iOS
   npm run ios
   
   # Android
   npm run android
   
   # Web
   npm run web
   ```

## 📦 主要依赖

```json
{
  "dependencies": {
    "expo": "~53.0.13",
    "react": "19.0.0",
    "react-native": "0.79.4",
    "@tamagui/core": "^1.129.4",
    "@tamagui/config": "^1.129.4",
    "@react-navigation/native": "^6.1.9",
    "@react-navigation/bottom-tabs": "^6.5.11",
    "@react-navigation/native-stack": "^6.9.17",
    "zustand": "^5.0.2",
    "react-native-safe-area-context": "4.14.0",
    "react-native-screens": "4.4.0",
    "@expo/vector-icons": "^14.1.0"
  }
}
```

## ✨ 优化特性

### UI/UX 优化
- **现代化设计系统**: 基于iOS Human Interface Guidelines的8pt网格系统
- **主题一致性**: 深色/浅色主题在所有组件中的完美适配
- **视觉层次**: 优化的字体大小、间距、颜色对比度
- **交互反馈**: 触摸缩放动画、状态变化过渡效果
- **无障碍设计**: 符合WCAG标准的颜色对比度

### 性能优化
- **智能缓存**: 内存+持久化双层缓存系统
- **列表优化**: FlatList虚拟化、懒加载、批量渲染
- **组件优化**: React.memo、useCallback防止不必要重渲染
- **错误边界**: 全局错误捕获和优雅降级
- **TypeScript**: 完整的类型安全保障

### 交互体验
- **触摸反馈**: 自定义TouchableScale组件提供流畅的按压反馈
- **下拉刷新**: 主题适配的下拉刷新组件
- **搜索功能**: 实时搜索与历史记录
- **收藏系统**: 本地持久化收藏管理
- **导航优化**: 流畅的页面切换和返回动画

## 🎨 设计系统

### 颜色主题
- **主色调**: #007AFF (蓝色)
- **辅助色**: #5856D6 (紫色)
- **强调色**: #FF6B35 (橙色)
- **成功色**: #34C759 (绿色)
- **警告色**: #FF9500 (橙色)
- **错误色**: #FF3B30 (红色)

### 字体大小
- xs: 12px
- sm: 14px
- md: 16px (默认)
- lg: 18px
- xl: 20px
- 2xl: 24px
- 3xl: 30px
- 4xl: 36px

### 间距系统
- xs: 4px
- sm: 8px
- md: 16px (默认)
- lg: 24px
- xl: 32px
- 2xl: 48px
- 3xl: 64px

## 🔧 配置说明

### 主题配置
应用支持深色和浅色主题，主题状态通过 Zustand 管理，并持久化到本地存储。

### 导航配置
使用 React Navigation v6 实现：
- 底部标签导航 (主要页面)
- 堆栈导航 (页面跳转)
- 模态展示 (文章详情)

### 状态管理
使用 Zustand 管理全局状态：
- 应用状态 (主题、用户信息、收藏)
- 新闻状态 (文章列表、分类、加载状态)

## 📱 应用截图

*注：实际截图需要在应用运行后添加*

## 🚀 部署

### 构建应用
```bash
# 构建 Android APK
expo build:android

# 构建 iOS IPA
expo build:ios
```

### 发布到应用商店
```bash
# 发布到 Expo
expo publish

# 提交到应用商店
expo submit
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目链接: [项目仓库地址]

## 🙏 致谢

感谢以下开源项目和社区：
- React Native 团队
- Expo 团队
- Tamagui 团队
- React Navigation 团队
- Zustand 团队
