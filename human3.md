chainMix 是一个基于 React Native + Expo 技术栈开发的跨平台移动应用，融合AI智能分析，为用户提供最新的区块链和加密货币资讯。当前目录就是项目工程目录。
我有个后端服务在 @server 是基于 Node.js 聚合几个 API 作为提供服务的 API。
请帮我阅读前后端代码，然后帮我解决以下问题：
1. 核对前端页面和后端 API，查看前端页面模块是否合理。比如：资讯、快讯、文章、推特帖子、AI助手、发现等页面，是否合理、是否足够？查看后端 API 是否有对应实现。
2. 对接前后端 API，核对字段，请求方法、端口等。
3. 检查前后端字段是否合理，字段是否对齐。
4. 增加、或者修复你认为 P0 级别的任务。


----------------------------------------------------------------------------------

通过pnpm dev:web 可以在本地启动web服务，通过cd server && pnpm dev 可以启动后端API服务。你可以通过工具 playwright 查看页面。
0. 请阅读前后端源代码。
1. 我已经启动了前后端的服务。
2. 目前我已经有一版初步的前端UI，后端API代码。
3. 目前正在对接前后端API中  
    3.1 请帮我继续对接前后端API，包括但不局限于字段、接口、请求方法、端口等。
    3.2 请帮我实现所有涉及的细节。
4. 请查看页面 http://localhost:8081/ 并分析。  
    4.1 查看页面对接是否正确，还缺少什么功能？
    4.2 页面UI可以如何改善？
    4.3 是否缺少某些模块？
    4.4 是否缺少某些后端实现？
    4.5 是否缺少某些字段？
    4.6 尽可能每个路由都浏览，查看是否有上述问题？


----------------------------------------------------------------------------------

通过pnpm dev:web 可以在本地启动web服务，通过cd server && pnpm dev 可以启动后端API服务。你可以通过工具 playwright 查看页面。
以下需求特别针对前端页面的。
请查看页面 http://localhost:8081/ 并分析。

1. 访问每个路由页面并截图分析。  
2. 查看每个页面实现是否正确，合理，是否缺少某些字段？
3. 页面布局是否合理，如何改善？
4. 是否缺少某些模块？是否缺少某些后端实现？
5. 尽可能每个路由都浏览并截图，查看是否有上述问题。


----------------------------------------------------------------------------------

1. 我并不需要 NetInfo 和 useNetworkStatus 这种逻辑，因为没有这么强的让用户知道网络是否连接的需求，我只需要请求失败的时候，有个小tips提示。请帮我移除 useNetworkStatus  相关逻辑，增加tips相关逻辑。

2. 页面主要场景是移动端，请resize到移动端大小调试。

3. 由于有SPA逻辑，expo动态更新做的并没有太好。修改页面后，请刷新页面查看是否正常。

4. 请继续帮我优化 各个 页面的布局问题，排版问题。


----------------------------------------------------------------------------------
1. 主页推特观点模块卡片大小好像不一致，帮我设置一致宽高，内容显示不完整的用省略号替代。点击 查看全部 按钮进入 推特观点 列表页，列表页可以不限制高度，展示推特全文（默认一致高度，如果有额外内容，可以点击切换显示全部）。
2. 首页的热门资讯模块再优化下排版
3. 首页好像还有很多地方没有接入真实的接口，请列出详细需要的接口和字段
4. 快讯页面似乎右边内容被遮挡了，合理优化排版
5. 首页，快讯、发现页面应该都支持下拉刷新
6. 所有页面都是移动端展示
7. 请帮我修复上述所有问题。

----------------------------------------------------------------------------------

1. handleTwitterPostPress 点击推特帖子，有省略号的帖子，仍然没有展开/省略 切换
2. 首页 推特观点 模块，帖子内容展示有截断
3. 首页 热门资讯模块 热门资讯列表展示太难看了，请优化，可以排版紧凑点
4. 快讯页面仍然右边有截断。
5. 请以移动端视角查看具体前端页面分析，然后修复上述问题。


----------------------------------------------------------------------------------

1. 帮我写一个启动mongodb docker的脚本 scripts/mongo.sh
2. 帮我阅读分析：@server/src/draft 目录所有文件
2.1 文件包含了多个API，包括请求header，url，params，body以及返回示例。
2.2 请你帮我写一个相对独立的逻辑，递归抓取这些API数据保存到对应的表中，不同的API甚至可以使用不同数据库，阅读源码来决定，使用mongodb
2.3 再根据保存的数据，提供API服务。


----------------------------------------------------------------------------------
请阅读源码示例目录 @server/src/draft 。这个目录有chaincatcher followin odaily techflow trendx chainfeeds foresightnews panewslab theblockbeats 9 个API源的ts文件，或者文件夹。如果是一个ts文件，那么阅读这个ts 文件，然后根据文件内容的注释、示例，可以看到这个API的请求、返回等详细信息。如果是文件夹，那么每个ts文件可能包括了不同类型的信息的API请求(包括端口，params, body, methods, headers等)、返回定义信息。而foresightnews稍微复杂，所有的请求都在foresightnews.ts，返回需要foresightnews.decode.ts解码，json文件是返回示例。
请你根据现有源码示例，以及目前项目的架构，帮我实现：
1. 为每个API源实现一个scraper，递归抓取所有信息。第一次递归抓取所有信息，支持“断点续抓”。后续再次运行可以抓取最新信息，支持强制抓取所有历史信息。
2. 为每个API源设计合理的数据库，或者表，最好能保存每个API源的原有信息，用mongodb实现。
3. 阅读现有项目源码再进行改动，如果有重复的部分，删除重复的部分。


----------------------------------------------------------------------------------
请再次仔细阅读源码示例目录 @server/src/draft 。这个目录有chaincatcher followin odaily techflow trendx chainfeeds foresightnews panewslab theblockbeats 9 个API源的ts文件，或者文件夹。如果是一个ts文件，那么阅读这个ts 文件，然后根据文件内容的注释、示例，可以看到这个API的请求、返回等详细信息。如果是文件夹，那么每个ts文件可能包括了不同类型的信息的API请求(包括端口，params, body, methods, headers等)、返回定义信息。而foresightnews稍微复杂，所有的请求都在foresightnews.ts，返回需要foresightnews.decode.ts解码，json文件是返回示例。
API必须包括：
<api-list>
techflow:
    - flash 快讯: https://www.techflowpost.com/ashx/newflash_index.ashx
panewslab:
    - flash 快讯：https://api.panewslab.com/webapi/flashnews?rn=20&lid=1&apppush=0&lastTime=1752130599
chaincatcher:
    - flash 快讯：https://www.chaincatcher.com/pc/content/page?channel=PC&cversion=1.0.0&requestid=PC&{20250730163312119}&{f7d3fb15-ff38-4ba9-a609-56d74786f252}
    - article 文章：https://www.chaincatcher.com/pc/content/page?channel=PC&cversion=1.0.0&requestid=PC&{20250730163312119}&{f7d3fb15-ff38-4ba9-a609-56d74786f252}
    - 
followin:
    - flash 快讯：https://api.followin.io/feed/list/recommended/news
odaily:
    - depth 文章：https://web-api.odaily.news/depth/page?page=2&size=1&sortType=latest
    - flash 快讯：https://web-api.odaily.news/newsflash/page?page=2&size=16&isImport=false&groupId=0
    - post 文章：https://web-api.odaily.news/post/page?page=1&size=16
    - token price：https://web-api.odaily.news/market/listBySymbols?symbols[]=btcusdt&symbols[]=ethusdt&symbols[]=htxusdt&symbols[]=solusdt&symbols[]=bnbusdt&exchange=1
    - topic 话题：https://web-api.odaily.news/newsflash/page?page=2&size=16&isImport=false&groupId=0
    - tweet https://web-api.odaily.news/viewpoint/page?page=2&size=16
trendx:
    - tweet 信息：https://www.trendx.tech/v1/dao/public/topics/388/tweets?page=1&rows=15
    - news 新闻：https://www.trendx.tech/v1/dao/public/news/articles?page=1&rows=100
    - financing 信息：https://www.trendx.tech/v1/dao/public/projects/financing?projectName=&page=1&rows=20
chainfeeds:
    - 资讯：https://api.chainfeeds.xyz/feed/list?page=1&page_size=20&group_alias=selected
    - 精选：https://api.chainfeeds.xyz/feed/list?page=1&page_size=20&group_alias=flash
    - 主题新闻：https://api.chainfeeds.xyz/theme/list?page=1&page_size=10&language=zh-cn
    - chainfeeds 原创文章：https://api.chainfeeds.xyz/subject/list?page=1&page_size=10
foresightnews:
    - flash 快讯："https://api.foresightnews.pro/v1/dayNews?date=20250730",
    - news 新闻 ："https://api.foresightnews.pro/v1/news?page=1&size=20",
    - feed 订阅源："https://api.foresightnews.pro/v2/feed?page=1&size=20",
    - event 事件："https://api.foresightnews.pro/v1/events?page=2&size=20",
    - topic 专题："https://api.foresightnews.pro/v1/topics?page=1&size=20",
    - column 专栏："https://api.foresightnews.pro/v1/columns?page=1&size=20&search=&is_hot=true",
    - article 某个专栏的article："https://api.foresightnews.pro/v1/articles?page=1&size=20&column_id=721",
    - tools 工具集："https://api.foresightnews.pro/v1/links2?page=1&size=18",
    - fundraising 融资信息："https://api.foresightnews.pro/v1/fundraising?page=2&size=20&search=&sort_by=&sort=&min_amount=&max_amount=&round=&start_time=&end_time=",
    - calendars 某个日期日历事件提醒："https://api.foresightnews.pro/v1/calendars?month_date=20250801",
theblockbeats:
    - flash 快讯：https://api.blockbeats.cn/v2/newsflash/list?page=2&limit=10&ios=-2&end_time=1752136666&detective=-2
    - finance 融资信息：https://api.blockbeats.cn/v2/financing/list?page=2&limit=10&start_time=&end_time=&min_money=&max_money=&title=

</api-list>
**重要**：不同API可能请求method不同，params不同，body不同，headers不同。并且每个API请求务必加上合理的headers

现有代码如果有多余的实现，重复的实现，多余的代码等，在不影响其他功能情况下，可以直接删除。



----------------------------------------------------------------------------------
现有基于Node.js的后端API项目位于 @server 目录。请阅读相关源码然后帮我修复以下问题：
1. @server/src/app.ts 文件的114-122行似乎有重复定义。
2. @server/src/routes 目录中的API定义有v2版本，但我并不需要v2，只需要在原有代码上修改即可。帮我检查相关代码逻辑，去除v2文件，直接在原有代码中修复即可，请确保逻辑正确，是v2版本的逻辑。
3. 原有v2版本代码可能还有错误，比如未引入变量等，请确保修复了所有问题，代码能正常运行。
4. 



----------------------------------------------------------------------------------

那么，请你再看看 @server/src/app.ts 的逻辑。提供API服务的代码实现是否正确？ dataScheduler 似乎不需要了，因为已经有了专门的scraper逻辑，相应逻辑是否可以删除？



----------------------------------------------------------------------------------
请阅读当前目录 react-native + expo 的区块链资讯 App 项目源代码。
前端代码逻辑位于： @src
前端 API 请求逻辑位于： @src/services/api.ts

提供后端 API 服务的源代码位于： @server。
后端 API 服务主要包括一个 scraper 爬虫和对前端提供 API 服务的逻辑。
后端 scraper 逻辑位于： @server/src/scraper/scraper.ts
后端 API 逻辑位于：@server/src/app.ts

1.根据 scraper 爬虫保存的数据逻辑，核对后端 API(@server/src/app.ts) 服务涉及的路由接口参数、实现是否正确。
2.根据 scraper 爬虫保存的数据逻辑，核对前端 API(@src/services/api.ts) 服务涉及的路由接口参数、实现是否正确。
3.根据 scraper 保存数据结构，资源类型、等信息，查看前端页面是否有需要修改的地方。
4.检查前端页面所有需要接口的地方，核查后端是否有对应接口实现，实现是否正确（请求 URL，method，params，body，接口返回字段等）




----------------------------------------------------------------------------------

前端项目改动：
首页：
    1. 精选文章：后端数据筛选条件为： dataType 为 article，必须有封面图 cover 字段，title 字段包含：“精选”，返回最近的 5 条
    2. 推特观点：后端数据筛选条件为： dataType 为 tweet，返回最近的 5 条。点击右侧查看全部时候，查询条件为 dataType 为 tweet，limit 10，分页查询。
    3. 热门资讯：后端数据筛选条件为： dataType 为 news，返回最近的 5 条，必须有封面图 cover 字段。点击右侧查看全部时候，查询条件为 dataType 为 news，limit 10，分页查询。
快讯页面：
    1. 点击右上角刷新按钮能强制刷新，如果停留当前页面，30s 一次强制刷新。如果其他进入当前页面间隔超过 30s 进入当前页面的时候也刷新一次。
    2. 核对后端返回的数据与前端页面展示数据字段，目前显示了： Invalid Date • Unknown


----------------------------------------------------------------------------------

请帮我修复前端项目中所有的 TwitterPost 类型，以及对应使用的地方。
这个类型应该跟后端@server/src/scraper/scraper.ts返回 tweet 数据类型 DataSourceItem 一致



----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------




----------------------------------------------------------------------------------





----------------------------------------------------------------------------------





----------------------------------------------------------------------------------






