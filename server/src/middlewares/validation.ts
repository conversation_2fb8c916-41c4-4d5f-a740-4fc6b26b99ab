import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { BadRequestError, handleValidationError } from '@/middlewares/error';

/**
 * Middleware to validate request query parameters
 */
export const validateQuery = (schema: ZodSchema) => {
  return (req: Request, _: Response, next: NextFunction): void => {
    try {
      const validatedQuery = schema.parse(req.query);
      req.query = validatedQuery;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const appError = handleValidationError(error);
        next(appError);
      } else {
        next(new BadRequestError('Invalid query parameters'));
      }
    }
  };
};

/**
 * Middleware to validate request body
 */
export const validateBody = (schema: ZodSchema) => {
  return (req: Request, _: Response, next: NextFunction): void => {
    try {
      const validatedBody = schema.parse(req.body);
      req.body = validatedBody;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const appError = handleValidationError(error);
        next(appError);
      } else {
        next(new BadRequestError('Invalid request body'));
      }
    }
  };
};

/**
 * Middleware to validate request parameters
 */
export const validateParams = (schema: ZodSchema) => {
  return (req: Request, _: Response, next: NextFunction): void => {
    try {
      const validatedParams = schema.parse(req.params);
      req.params = validatedParams;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const appError = handleValidationError(error);
        next(appError);
      } else {
        next(new BadRequestError('Invalid request parameters'));
      }
    }
  };
};

/**
 * Generic request validation middleware
 */
export const validateRequest = (schemas: {
  query?: ZodSchema;
  body?: ZodSchema;
  params?: ZodSchema;
}) => {
  return (req: Request, _: Response, next: NextFunction): void => {
    try {
      // Validate query parameters
      if (schemas.query) {
        req.query = schemas.query.parse(req.query);
      }

      // Validate request body
      if (schemas.body) {
        req.body = schemas.body.parse(req.body);
      }

      // Validate URL parameters
      if (schemas.params) {
        req.params = schemas.params.parse(req.params);
      }

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const appError = handleValidationError(error);
        next(appError);
      } else {
        next(new BadRequestError('Request validation failed'));
      }
    }
  };
};