import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { CustomError } from '@/types';

/**
 * Custom error class for application-specific errors
 */
export class AppError extends Error implements CustomError {
  public statusCode: number;
  public code: string;
  public details?: Record<string, unknown>;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_SERVER_ERROR',
    details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details || undefined;

    // Maintain proper stack trace (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}

/**
 * HTTP error classes for common status codes
 */
export class BadRequestError extends AppError {
  constructor(message: string = 'Bad Request', details?: Record<string, unknown>) {
    super(message, 400, 'BAD_REQUEST', details);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized', details?: Record<string, unknown>) {
    super(message, 401, 'UNAUTHORIZED', details);
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden', details?: Record<string, unknown>) {
    super(message, 403, 'FORBIDDEN', details);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found', details?: Record<string, unknown>) {
    super(message, 404, 'NOT_FOUND', details);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Conflict', details?: Record<string, unknown>) {
    super(message, 409, 'CONFLICT', details);
  }
}

export class UnprocessableEntityError extends AppError {
  constructor(message: string = 'Unprocessable Entity', details?: Record<string, unknown>) {
    super(message, 422, 'UNPROCESSABLE_ENTITY', details);
  }
}

export class TooManyRequestsError extends AppError {
  constructor(message: string = 'Too Many Requests', details?: Record<string, unknown>) {
    super(message, 429, 'TOO_MANY_REQUESTS', details);
  }
}

export class InternalServerError extends AppError {
  constructor(message: string = 'Internal Server Error', details?: Record<string, unknown>) {
    super(message, 500, 'INTERNAL_SERVER_ERROR', details);
  }
}

export class BadGatewayError extends AppError {
  constructor(message: string = 'Bad Gateway', details?: Record<string, unknown>) {
    super(message, 502, 'BAD_GATEWAY', details);
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(message: string = 'Service Unavailable', details?: Record<string, unknown>) {
    super(message, 503, 'SERVICE_UNAVAILABLE', details);
  }
}

export class GatewayTimeoutError extends AppError {
  constructor(message: string = 'Gateway Timeout', details?: Record<string, unknown>) {
    super(message, 504, 'GATEWAY_TIMEOUT', details);
  }
}

/**
 * Determines if an error is operational (expected) or programming error
 */
const isOperationalError = (error: Error): boolean => {
  if (error instanceof AppError) {
    return true;
  }
  
  // Add other operational error types here
  return false;
};

/**
 * Formats error details for logging and response
 */
const formatErrorDetails = (error: Error): Record<string, unknown> => {
  const details: Record<string, unknown> = {};

  if (error instanceof AppError && error.details) {
    details.errorDetails = error.details;
  }

  // Add stack trace for non-production environments
  if (process.env.NODE_ENV !== 'production') {
    details.stack = error.stack;
  }

  return details;
};

/**
 * Creates standardized error response
 */
const createErrorResponse = (error: Error, requestId?: string): any => {
  const isAppError = error instanceof AppError;
  
  return {
    success: false,
    data: null,
    error: error.message,
    timestamp: new Date().toISOString(),
    ...(requestId && { requestId }),
    ...(isAppError && error.details && { details: error.details }),
  };
};

/**
 * Express error handling middleware
 * This should be the last middleware in the stack
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  _: NextFunction
): void => {
  // Generate request ID for tracing
  const requestId = req.headers['x-request-id'] as string || 
    `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // Log the error
  const errorLog = {
    requestId,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    error: {
      name: error.name,
      message: error.message,
      code: error instanceof AppError ? error.code : 'INTERNAL_SERVER_ERROR',
      statusCode: error instanceof AppError ? error.statusCode : 500,
      ...formatErrorDetails(error),
    },
  };

  // Log based on error severity
  if (error instanceof AppError && error.statusCode < 500) {
    // Client errors (4xx) - log as warnings
    logger.warn('Client error occurred', errorLog);
  } else {
    // Server errors (5xx) - log as errors
    logger.error('Server error occurred', errorLog);
  }

  // Don't expose internal errors in production
  let responseError = error;
  if (!isOperationalError(error) && process.env.NODE_ENV === 'production') {
    responseError = new InternalServerError('Something went wrong');
  }

  // Send error response
  const statusCode = responseError instanceof AppError ? responseError.statusCode : 500;
  const errorResponse = createErrorResponse(responseError, requestId);

  res.status(statusCode).json(errorResponse);
};

/**
 * Async error handler wrapper for route handlers
 * Catches async errors and passes them to error middleware
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<void>
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req: Request, _: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Validation error handler for Zod validation failures
 */
export const handleValidationError = (error: unknown): AppError => {
  if (error && typeof error === 'object' && 'issues' in error) {
    // Zod validation error
    const zodError = error as { issues: Array<{ path: string[]; message: string }> };
    const details = zodError.issues.reduce((acc, issue) => {
      const field = issue.path.join('.');
      acc[field] = issue.message;
      return acc;
    }, {} as Record<string, string>);

    return new BadRequestError('Validation failed', { validationErrors: details });
  }

  return new BadRequestError('Invalid request data');
};

/**
 * External API error handler
 */
export const handleExternalApiError = (error: unknown, apiName: string): AppError => {
  logger.error(`External API error from ${apiName}:`, error);

  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as { response: { status: number; data: unknown } };
    
    if (axiosError.response?.status === 429) {
      return new TooManyRequestsError(`Rate limit exceeded for ${apiName} API`);
    }
    
    if (axiosError.response?.status >= 500) {
      return new BadGatewayError(`${apiName} API is currently unavailable`);
    }
    
    if (axiosError.response?.status === 404) {
      return new NotFoundError(`Resource not found in ${apiName} API`);
    }
  }

  return new ServiceUnavailableError(`External service ${apiName} is unavailable`);
};