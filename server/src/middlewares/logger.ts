import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

/**
 * Request logging middleware
 * Logs incoming requests with relevant information
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // Generate or use existing request ID
  const requestId = (req.headers['x-request-id'] as string) || 
    `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // Add request ID to headers for response
  res.setHeader('x-request-id', requestId);

  // Log request details
  const requestInfo = {
    requestId,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    timestamp: new Date().toISOString(),
  };

  logger.info('Incoming request', requestInfo);

  // Override res.end to log response details
  const originalEnd = res.end;
  res.end = function(this: Response, chunk?: any, encoding?: BufferEncoding): Response {
    const endTime = Date.now();
    const duration = endTime - startTime;

    const responseInfo = {
      requestId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
    };

    // Log based on status code
    if (res.statusCode >= 400) {
      logger.warn('Request completed with error', responseInfo);
    } else {
      logger.info('Request completed successfully', responseInfo);
    }

    // Call original end method
    return originalEnd.call(this, chunk, encoding as BufferEncoding);
  };

  next();
};