import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * 标准化的API响应格式
 */
export interface StandardApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  timestamp: string;
  requestId?: string;
  metadata?: {
    version: string;
    endpoint: string;
    processingTime?: number;
    [key: string]: any;
  };
}

/**
 * 创建标准成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  metadata?: any,
  processingTime?: number
): StandardApiResponse<T> {
  return {
    success: true,
    data,
    timestamp: new Date().toISOString(),
    metadata: {
      version: '2.0.0',
      endpoint: '', // 会在中间件中填充
      processingTime,
      ...metadata
    }
  };
}

/**
 * 创建标准错误响应
 */
export function createErrorResponse(
  error: string,
  errorCode?: string,
  metadata?: any
): StandardApiResponse<null> {
  return {
    success: false,
    error,
    errorCode,
    timestamp: new Date().toISOString(),
    metadata: {
      version: '2.0.0',
      endpoint: '', // 会在中间件中填充
      ...metadata
    }
  };
}

/**
 * 错误处理中间件
 */
export function errorHandler(
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const requestId = req.headers['x-request-id'] as string || generateRequestId();
  const endpoint = `${req.method} ${req.path}`;
  
  // 记录错误
  logger.error(`API错误 [${requestId}] ${endpoint}:`, {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    query: req.query,
    headers: req.headers
  });

  // 根据错误类型确定HTTP状态码
  let statusCode = 500;
  let errorCode = 'INTERNAL_SERVER_ERROR';
  let userMessage = '服务器内部错误';

  if (error.name === 'ValidationError') {
    statusCode = 400;
    errorCode = 'VALIDATION_ERROR';
    userMessage = '请求参数验证失败';
  } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
    statusCode = 503;
    errorCode = 'DATABASE_ERROR';
    userMessage = '数据库服务不可用';
  } else if (error.message?.includes('timeout')) {
    statusCode = 504;
    errorCode = 'TIMEOUT_ERROR';
    userMessage = '请求超时';
  } else if (error.message?.includes('not found') || error.message?.includes('未找到')) {
    statusCode = 404;
    errorCode = 'NOT_FOUND';
    userMessage = '请求的资源不存在';
  } else if (error.message?.includes('unauthorized') || error.message?.includes('未授权')) {
    statusCode = 401;
    errorCode = 'UNAUTHORIZED';
    userMessage = '未授权访问';
  }

  const errorResponse = createErrorResponse(
    userMessage,
    errorCode,
    {
      endpoint,
      requestId,
      originalError: process.env.NODE_ENV === 'development' ? error.message : undefined
    }
  );

  // 填充端点信息
  errorResponse.metadata!.endpoint = endpoint;
  errorResponse.requestId = requestId;

  res.status(statusCode).json(errorResponse);
}

/**
 * 响应增强中间件 - 为所有响应添加标准格式
 */
export function responseEnhancer(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const requestId = req.headers['x-request-id'] as string || generateRequestId();
  const startTime = Date.now();
  const endpoint = `${req.method} ${req.path}`;

  // 保存原始的 json 方法
  const originalJson = res.json;

  // 重写 json 方法
  res.json = function(body: any) {
    const processingTime = Date.now() - startTime;

    // 如果响应已经是标准格式，只需要填充额外信息
    if (body && typeof body === 'object' && 'success' in body) {
      body.requestId = requestId;
      if (body.metadata) {
        body.metadata.endpoint = endpoint;
        body.metadata.processingTime = processingTime;
      }
    } else {
      // 如果不是标准格式，包装成标准格式
      body = createSuccessResponse(body, { endpoint }, processingTime);
      body.requestId = requestId;
    }

    return originalJson.call(this, body);
  };

  next();
}

/**
 * 请求日志中间件
 */
export function requestLogger(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const requestId = req.headers['x-request-id'] as string || generateRequestId();
  const startTime = Date.now();

  // 添加请求ID到请求头
  req.headers['x-request-id'] = requestId;

  // 记录请求开始
  logger.info(`API请求开始 [${requestId}] ${req.method} ${req.path}`, {
    url: req.url,
    method: req.method,
    userAgent: req.headers['user-agent'],
    ip: req.ip,
    query: req.query
  });

  // 监听响应完成
  res.on('finish', () => {
    const processingTime = Date.now() - startTime;
    logger.info(`API请求完成 [${requestId}] ${req.method} ${req.path}`, {
      statusCode: res.statusCode,
      processingTime: `${processingTime}ms`,
      contentLength: res.get('content-length')
    });
  });

  next();
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * 包装异步路由处理器以自动捕获异常
 */
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 速率限制错误
 */
export class RateLimitError extends Error {
  constructor(message: string = '请求过于频繁，请稍后重试') {
    super(message);
    this.name = 'RateLimitError';
  }
}

/**
 * 验证错误
 */
export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * 数据库错误
 */
export class DatabaseError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DatabaseError';
  }
}

/**
 * 服务不可用错误
 */
export class ServiceUnavailableError extends Error {
  constructor(message: string = '服务暂时不可用') {
    super(message);
    this.name = 'ServiceUnavailableError';
  }
}