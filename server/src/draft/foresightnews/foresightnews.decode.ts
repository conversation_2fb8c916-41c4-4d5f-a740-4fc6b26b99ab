import * as pako from 'pako'; // 假设已安装 pako 库 (npm install pako @types/pako)

/**
 * 递归解析对象中可能被压缩编码的属性。
 * 假设压缩编码属性是 Base64 编码的 Zlib 压缩字符串（以 'eJ' 开头）。
 * 解码后假设为 JSON 字符串，并解析为对象。
 * 支持嵌套对象和数组。
 *
 * @param obj 要解析的对象
 * @returns 解析后的对象（原地修改，但返回相同引用以便链式调用）
 */
export function parseCompressedProperties<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    // 检查是否可能是压缩字符串：长度 > 0 且以 'eJ' 开头（Zlib + Base64 常见前缀）
    if (obj.length > 0 && obj.startsWith('eJ')) {
      try {
        // Step 1: Base64 解码
        const binaryString = atob(obj);

        // Step 2: 转换为 Uint8Array
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        // Step 3: Zlib 解压缩
        const decompressed = pako.inflate(bytes, { to: 'string' });

        // Step 4: 假设解压后是 JSON 字符串，解析为对象
        return JSON.parse(decompressed) as T;
      } catch (error) {
        console.error('Failed to parse compressed string:', error);
        return obj; // 解析失败，返回原值
      }
    }
    return obj; // 不是压缩字符串，直接返回
  }

  if (Array.isArray(obj)) {
    // 递归处理数组每个元素
    obj.forEach((item, index) => {
      obj[index] = parseCompressedProperties(item);
    });
    return obj;
  }

  if (typeof obj === 'object') {
    // 递归处理对象每个属性
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        obj[key] = parseCompressedProperties(obj[key]);
      }
    }
    return obj;
  }

  // 其他类型（如 number, boolean）直接返回
  return obj;
}

if (require.main === module) {
  const response = {
    code: 1,
    data: "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",
    msg: ''
  };

  const parsed = parseCompressedProperties(response);
  console.log(parsed);
}