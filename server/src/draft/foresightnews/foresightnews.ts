import { writeFileSync } from "node:fs"
import { parseCompressedProperties } from "./foresightnews.decode"
import path from "node:path"

const headers = {
  "accept": "application/json, text/plain, */*",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
  "cache-control": "no-cache",
  "pragma": "no-cache",
  "priority": "u=1, i",
  "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  "sec-ch-ua-mobile": "?0",
  "sec-ch-ua-platform": "\"Windows\"",
  "sec-fetch-dest": "empty",
  "sec-fetch-mode": "cors",
  "sec-fetch-site": "same-site",
  "x-requested-with": "XMLHttpRequest",
  "cookie": "ssxmod_itna=Qui=1GCb0KiIxYQiQo0=GR3tD=tG77DwxBP017sDuxiK08D6BDBR0QDtjmTFkTCDGxB+oyUr8Dv1DBkbxbDnqD80DQeDvYxwu=GM77x7xvT8S0b+omCWGx0IC4UQx0k=xRh2b0dhOv6k0oHrz7FHYr4DHxi8DB9DtjIHDenaDCeDQxirDD4DACPDFxibDinAT4DdC3fEAREp7DGrDlKDRo4KC4GWDiPD72Oih2OXdRoDTxiaDDpIM/oiDGvi41I7Dm4vpUA6wDD1qWSeT0oD9E4DsaiSD2oD0EtMD68E+Tt9dOo6n40OD0IUKawe2Q/WLtcm098B0bSDol+qtbx8RD3b44RD7GbYGiATxbDxFT4U2eCDqKGPCimWwHCooDiTiCIzhqi+eMusxmTqjGmBYTiNIQxdEhS2dElm+hxyD=LhxMA5tB5a7f+2dGjxCipk75Y144D; ssxmod_itna2=Qui=1GCb0KiIxYQiQo0=GR3tD=tG77DwxBP017sDuxiK08D6BDBR0QDtjmTFkTCDGxB+oyUr8D5bD8+eibthKZgT5Q9cu2X38xFGSWmtPD",
  "Referer": "https://foresightnews.pro/",
  "Origin": "https://foresightnews.pro",
  "Referrer-Policy": "strict-origin-when-cross-origin"
}
const endpoints = {
  // flash 快讯
  dayNews: "https://api.foresightnews.pro/v1/dayNews?date=20250730",
  // 新闻 
  news: "https://api.foresightnews.pro/v1/news?page=1&size=20",
  // 订阅源
  feed: "https://api.foresightnews.pro/v2/feed?page=1&size=20",
  // event 事件
  event: "https://api.foresightnews.pro/v1/events?page=2&size=20",
  // topic 专题
  topic: "https://api.foresightnews.pro/v1/topics?page=1&size=20",
  // column 专栏
  column: "https://api.foresightnews.pro/v1/columns?page=1&size=20&search=&is_hot=true",
  // article 某个专栏的article
  article: "https://api.foresightnews.pro/v1/articles?page=1&size=20&column_id=721",
  // tools 工具集
  tools: "https://api.foresightnews.pro/v1/links2?page=1&size=18",
  // fundraising 融资信息
  fundraising: "https://api.foresightnews.pro/v1/fundraising?page=2&size=20&search=&sort_by=&sort=&min_amount=&max_amount=&round=&start_time=&end_time=",
  // calendars 某个日期日历事件提醒
  calendars: "https://api.foresightnews.pro/v1/calendars?month_date=20250801",
  // article-detail
  articleDetail: "https://api.foresightnews.pro/v1/article/88250"
} as const;

const fetchData = async (type: keyof typeof endpoints) => {
  await fetch(endpoints[type], {
    headers,
    "method": "GET"
  })
    .then(res => res.json())
    .then(res => {
      const data = parseCompressedProperties(res)
      console.log(data)
      writeFileSync(path.resolve(__dirname, `foresightnews.${type}.json`), JSON.stringify(data, null, 2))
    })
}
const run = async () => {
  for (const endpoint in endpoints) {
    if (endpoint !== 'articleDetail') continue
    await fetchData(endpoint as keyof typeof endpoints)
  }
}
run()