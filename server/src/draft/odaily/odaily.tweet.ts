// tweet
fetch('https://web-api.odaily.news/viewpoint/page?page=2&size=1', {
  headers: {
    accept: 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    authorization: 'Bearer undefined',
    'cache-control': 'no-cache',
    pragma: 'no-cache',
    priority: 'u=1, i',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'x-locale': 'zh-CN',
    Referer: 'https://www.odaily.news/viewpoint',
    Origin: 'https://www.odaily.news',
  },
  body: null,
  method: 'GET',
});
// 话题tweet url
const topicUrl = (entityUrl: string) => entityUrl
const response = {
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 285,
    "pageSize": 1,
    "pageNum": 2,
    "list": [
      {
        "id": 315,
        "groupId": null,
        "twId": "nine_DeFi",
        "name": "nine_🦇🔊",
        "cover": "https://oss.odaily.top/image/2025/07/25/473ee0163f6545bca05d30ae6c9611b7.png",
        "summary": "链上关注的币：\nSol上：其实在我眼里bonk只有两种币：Gp+useless & 新攒的局\n1. $Moby 搞了发射台，对这个项目不是太懂，有懂哥吗？\n2. $aura 看看能不能回踩下0.16左右\n3. $uranus 8-9m波段即可\n4. $memecoin 看看后面回踩不回踩25M左右\n5. $ani 我觉得这个项目基本结束了 ，要去博的话还是30M\n6. $bluechip 8-9m又可以买波段了\n7. $kori 已经到位置，跌破损\nBase上不是太懂，需要人给我讲讲，Bsc还是单机盘\n\n最近关注的项目：\n1.@ssidotfun  swarms 搞的类似bonkfun的，看看后面能不能搞起来吧\n2.@FreeStyle_Web3 街头篮球，可能要发币\n3 .@trendsdotfun 不知道他们能不能搞起来\n4. @baseapp 看看咋玩了\n\n交易所：\n大饼这几天如约而至的回调了但是依旧看好这几个月。这几天二级妖币比较多，但是很多主流币都被打下来了。个人准备休息几天，自己这段时间的操作有点菜。",
        "isShowTitle": true,
        "state": 2,
        "accountNumber": "nine_DeFi",
        "isCuration": true,
        "source": "Twitter",
        "entityUrl": "https://x.com/nine_DeFi/status/1948638659391225949",
        "coverList": null,
        "createTimestamp": *************
      }
    ],
    "empty": false,
    "hasMore": true
  },
  "success": true,
  "serverTimestamp": *************
}
export {};
