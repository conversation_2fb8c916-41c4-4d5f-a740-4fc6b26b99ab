// price
fetch('https://web-api.odaily.news/market/listBySymbols?symbols[]=btcusdt&symbols[]=ethusdt&symbols[]=htxusdt&symbols[]=solusdt&symbols[]=bnbusdt&exchange=1', {
  headers: {
    accept: 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    authorization: 'Bearer undefined',
    'cache-control': 'no-cache',
    pragma: 'no-cache',
    priority: 'u=1, i',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'x-locale': 'zh-CN',
    Referer: 'https://www.odaily.news/tool',
    Origin: 'https://www.odaily.news',
  },
  body: null,
  method: 'GET',
});
const response = {
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "symbol": "bnbusdt",
      "name": "bnbusdt",
      "price": "805.34",
      "rate24H": "1.1174155420076003E-4",
      "isUp": false
    },
    {
      "symbol": "btcusdt",
      "name": "btcusdt",
      "price": "118290.81",
      "rate24H": "0.007519078127633736",
      "isUp": true
    },
    {
      "symbol": "ethusdt",
      "name": "ethusdt",
      "price": "3827.0",
      "rate24H": "0.01721314961299657",
      "isUp": true
    },
    {
      "symbol": "htxusdt",
      "name": "htxusdt",
      "price": "2.182E-6",
      "rate24H": "0.0022862368541380668",
      "isUp": false
    },
    {
      "symbol": "solusdt",
      "name": "solusdt",
      "price": "181.6801",
      "rate24H": "0.004978979975661074",
      "isUp": true
    }
  ],
  "success": true,
  "serverTimestamp": 1753858295082
}

export {};
