// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.LOG_LEVEL = 'error';
process.env.CACHE_TTL_MINUTES = '1';
process.env.NEWS_API_KEY = 'test-news-api-key';
process.env.COINGECKO_API_KEY = 'test-coingecko-api-key';

// Global test setup
beforeAll(() => {
  // Setup global test configuration
});

afterAll(() => {
  // Cleanup global test resources
});

beforeEach(() => {
  // Setup before each test
});

afterEach(() => {
  // Cleanup after each test
});