import { Router, Request, Response } from 'express';
import { validateQuery, validateParams } from '@/middlewares/validation';
import { z } from 'zod';
import { getInitializedScraper } from '../scraper/scraper-manager';
import { logger } from '../utils/logger';
import { asyncHandler, createSuccessResponse } from '../middlewares/api-response';

const router: Router = Router();

// Validation schemas for parameters
const ArticleIdParamsSchema = z.object({
  id: z.string().min(1, 'Article ID is required'),
});

const LimitQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(50).default(10),
});

// Enhanced validation schemas for v2 features
const NewsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sources: z.string().optional(),
  category: z.string().optional(),
  sortBy: z.enum(['publishTime', 'relevance', 'popularity']).default('publishTime'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

const SearchQuerySchema = z.object({
  q: z.string().min(1, '搜索关键词不能为空'),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20),
  sources: z.string().optional(),
  sortBy: z.enum(['publishTime', 'relevance']).default('publishTime'),
});

/**
 * @route GET /api/news
 * @desc Get aggregated news data from all sources
 * @access Public
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 * @query {string} sources - Comma-separated source names (optional)
 * @query {string} category - Category filter (optional)
 * @query {string} sortBy - Sort field (publishTime, relevance, popularity)
 * @query {string} sortOrder - Sort order (asc, desc)
 * @query {number} hours - Time range in hours (default: 24, max: 168)
 */
router.get(
  '/',
  validateQuery(NewsQuerySchema),
  asyncHandler(async (req: Request, res: Response) => {
    const concurrentScraper = await getInitializedScraper();

    const { page, limit, sources, category, sortBy, sortOrder } = req.query as any;

    // Determine target sources
    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;

    // Get news type data (articles, depth analysis, etc.)
    const newsDataTypes = ['article', 'depth', 'post', 'news', 'subject', 'column'];

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: category || '',
      sources: targetSources,
      dataTypes: newsDataTypes,
      limit: limit * 2,
      sortBy: sortBy === 'publishTime' ? 'publishTime' : 'publishTime',
    });

    // Filter and process results
    let filteredNews = searchResults.aggregatedResults.sort((a, b) => {
      if (sortBy === 'publishTime') {
        return sortOrder === 'asc'
          ? new Date(a.publishTime).getTime() - new Date(b.publishTime).getTime()
          : new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime();
      }
      return 0;
    });

    // Pagination
    const startIndex = (page - 1) * limit;
    const paginatedNews = filteredNews.slice(startIndex, startIndex + limit);

    // Source statistics
    const sourceStats = filteredNews.reduce((acc: any, item: any) => {
      acc[item.source] = (acc[item.source] || 0) + 1;
      return acc;
    }, {});

    res.json(
      createSuccessResponse({
        items: paginatedNews,
        pagination: {
          page,
          limit,
          total: filteredNews.length,
          pages: Math.ceil(filteredNews.length / limit),
          hasNext: startIndex + limit < filteredNews.length,
          hasPrev: page > 1,
        },
        metadata: {
          sourceDistribution: sourceStats,
          searchTime: searchResults.searchTime,
          totalSources: Object.keys(searchResults.sources).length,
          appliedFilters: {
            sources: targetSources,
            category,
            sortBy,
            sortOrder,
          },
        },
      }),
    );
  }),
);

/**
 * @route GET /api/news/search
 * @desc Search news across all sources
 * @access Public
 */
router.get('/search', validateQuery(SearchQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { q, page, limit, sources, sortBy } = req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;
    const newsDataTypes = ['article', 'depth', 'post', 'news', 'subject', 'column'];

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: q,
      sources: targetSources,
      dataTypes: newsDataTypes,
      limit: limit * 3,
      sortBy: sortBy === 'publishTime' ? 'publishTime' : 'publishTime',
    });

    // Pagination
    const startIndex = (page - 1) * limit;
    const paginatedResults = searchResults.aggregatedResults.slice(startIndex, startIndex + limit);

    res.json({
      success: true,
      data: {
        keyword: q,
        items: paginatedResults,
        pagination: {
          page,
          limit,
          total: searchResults.totalResults,
          pages: Math.ceil(searchResults.totalResults / limit),
          hasNext: startIndex + limit < searchResults.totalResults,
          hasPrev: page > 1,
        },
        metadata: {
          searchTime: searchResults.searchTime,
          sourceBreakdown: searchResults.sources,
          totalSources: targetSources?.length || Object.keys(searchResults.sources).length,
        },
      },
    });
  } catch (error) {
    logger.error('搜索新闻失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '搜索新闻失败',
    });
  }
});

/**
 * @route GET /api/news/trending
 * @desc Get trending news based on interaction metrics
 * @access Public
 */
router.get('/trending', async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

    const newsDataTypes = ['article', 'depth', 'post', 'news'];

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      dataTypes: newsDataTypes,
      limit: limit * 5,
      sortBy: 'publishTime',
    });

    // Filter recent data and sort by trending score
    const trendingNews = searchResults.aggregatedResults
      .map(item => ({
        ...item,
        hotScore:
          (item.metadata?.likeCount || 0) * 3 +
          (item.metadata?.readCount || 0) * 2 +
          (item.metadata?.shareCount || 0) * 5 +
          (item.tags?.length || 0) * 10 +
          (new Date().getTime() - new Date(item.publishTime).getTime() < 3600000 ? 50 : 0),
      }))
      .sort((a, b) => b.hotScore - a.hotScore)
      .slice(0, limit);

    res.json({
      success: true,
      data: {
        items: trendingNews,
        metadata: {
          algorithm: 'hotScore: likes*3 + reads*2 + shares*5 + tags*10 + recent_bonus',
          totalCandidates: searchResults.totalResults,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取热门新闻失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取热门新闻失败',
    });
  }
});

/**
 * @route GET /api/news/featured
 * @desc Get featured news (high quality content)
 * @access Public
 */
router.get('/featured', async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const limit = Math.min(parseInt(req.query.limit as string) || 5, 20);

    const featuredDataTypes = ['depth', 'column', 'article', 'subject'];

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      dataTypes: featuredDataTypes,
      limit: limit * 3,
      sortBy: 'publishTime',
    });

    // Filter and score featured content
    const featuredNews = searchResults.aggregatedResults
      .map(item => ({
        ...item,
        featuredScore:
          (item.content?.length || 0) * 0.01 +
          (item.metadata?.readCount || 0) * 0.1 +
          (item.metadata?.importance || 0) * 20 +
          (item.dataType === 'depth' ? 30 : 0) +
          (item.dataType === 'column' ? 25 : 0) +
          (item.author ? 10 : 0) +
          (item.tags?.length || 0) * 5,
      }))
      .sort((a, b) => b.featuredScore - a.featuredScore)
      .slice(0, limit);

    res.json({
      success: true,
      data: {
        items: featuredNews,
        metadata: {
          algorithm:
            'featuredScore: content_length*0.01 + reads*0.1 + importance*20 + type_bonus + author_bonus + tags*5',
          priorityTypes: featuredDataTypes,
          totalCandidates: searchResults.totalResults,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取精选新闻失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取精选新闻失败',
    });
  }
});

/**
 * @route GET /api/news/categories
 * @desc Get news categories based on tags and data types
 * @access Public
 */
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const stats = await concurrentScraper.getAllSourcesStats();

    // Extract category information
    const dataTypeCategories: any = {};

    // Count data types
    for (const [source, sourceStats] of Object.entries(stats.sources)) {
      if (sourceStats.itemsBySource) {
        sourceStats.itemsBySource.forEach((item: any) => {
          const dataType = item._id.dataType;
          if (!dataTypeCategories[dataType]) {
            dataTypeCategories[dataType] = {
              name: dataType,
              displayName: getDataTypeDisplayName(dataType),
              count: 0,
              sources: [],
            };
          }
          dataTypeCategories[dataType].count += item.count;
          if (!dataTypeCategories[dataType].sources.includes(source)) {
            dataTypeCategories[dataType].sources.push(source);
          }
        });
      }
    }

    // Predefined main categories
    const mainCategories = [
      { key: 'all', name: '全部', count: stats.totalItems },
      { key: 'news', name: '新闻资讯', types: ['article', 'news', 'post'], count: 0 },
      { key: 'flash', name: '快讯', types: ['flash'], count: 0 },
      { key: 'analysis', name: '深度分析', types: ['depth', 'column', 'subject'], count: 0 },
      {
        key: 'finance',
        name: '融资信息',
        types: ['finance', 'financing', 'fundraising'],
        count: 0,
      },
      { key: 'price', name: '价格数据', types: ['price'], count: 0 },
      { key: 'social', name: '社交观点', types: ['tweet'], count: 0 },
    ];

    // Calculate main category counts
    mainCategories.forEach(category => {
      if (category.types) {
        category.count = category.types.reduce((sum: number, type: string) => {
          return sum + (dataTypeCategories[type]?.count || 0);
        }, 0);
      }
    });

    res.json({
      success: true,
      data: {
        mainCategories: mainCategories.filter(cat => cat.count > 0),
        dataTypeCategories: Object.values(dataTypeCategories),
        sourceCategories: Object.keys(stats.sources).map(source => ({
          key: source,
          name: source.charAt(0).toUpperCase() + source.slice(1),
          count: stats.sources[source].totalItems,
          database: stats.sources[source].database,
        })),
        metadata: {
          totalCategories: Object.keys(dataTypeCategories).length,
          totalSources: Object.keys(stats.sources).length,
          generatedAt: stats.generatedAt,
        },
      },
    });
  } catch (error) {
    logger.error('获取新闻分类失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取新闻分类失败',
    });
  }
});

/**
 * @route GET /api/news/by-source/:source
 * @desc Get news from specific source
 * @access Public
 */
router.get('/by-source/:source', async (req: Request, res: Response): Promise<void> => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { source } = req.params;
    const page = Math.max(parseInt(req.query.page as string) || 1, 1);
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const dataType = req.query.dataType as string;

    const sourceDatabase = concurrentScraper.getSourceDatabase(source);
    if (!sourceDatabase) {
      res.status(404).json({
        success: false,
        error: `数据源 ${source} 不存在或未初始化`,
      });
      return;
    }

    const collection = sourceDatabase.collection('scraped_items');

    const filter: any = { source };
    if (dataType) {
      filter.dataType = dataType;
    }

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      collection.find(filter).sort({ publishTime: -1 }).skip(skip).limit(limit).toArray(),
      collection.countDocuments(filter),
    ]);

    res.json({
      success: true,
      data: {
        source,
        items,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: skip + limit < total,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error(`获取数据源 ${req.params.source} 新闻失败:`, error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取数据源新闻失败',
    });
  }
});

/**
 * @route GET /api/news/:id
 * @desc Get specific news article by ID (cross-source search)
 * @access Public
 * @param {string} id - Article ID
 */
router.get(
  '/:id',
  validateParams(ArticleIdParamsSchema),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const concurrentScraper = await getInitializedScraper();

      const { id } = req.params;

      // Search across all sources for this ID
      const searchResults = await concurrentScraper.searchAcrossAllSources({
        keyword: id,
        limit: 10,
        sortBy: 'publishTime',
      });

      // Find exact match
      const article = searchResults.aggregatedResults.find(
        item => item.id === id || item.id.includes(id),
      );

      if (!article) {
        res.status(404).json({
          success: false,
          error: '文章未找到',
        });
        return;
      }

      res.json({
        success: true,
        data: {
          article,
          metadata: {
            source: article.source,
            dataType: article.dataType,
            database: concurrentScraper.getSourceDatabaseMapping()[article.source],
          },
        },
      });
    } catch (error) {
      logger.error(`获取文章 ${req.params.id} 详情失败:`, error);
      res.status(500).json({
        success: false,
        error: (error as any)?.message || '获取文章详情失败',
      });
    }
  },
);

/**
 * Get data type display name
 */
function getDataTypeDisplayName(dataType: string): string {
  const displayNames: Record<string, string> = {
    article: '文章',
    flash: '快讯',
    depth: '深度分析',
    post: '帖子',
    news: '新闻',
    feed: '订阅源',
    topic: '主题',
    subject: '专题',
    column: '专栏',
    finance: '融资',
    financing: '融资信息',
    fundraising: '募资',
    price: '价格',
    tweet: '推文',
    tools: '工具',
    event: '事件',
    calendars: '日历',
    dayNews: '日报',
  };

  return displayNames[dataType] || dataType;
}

export default router;
