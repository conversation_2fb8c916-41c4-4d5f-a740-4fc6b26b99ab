import { Router, Request, Response } from 'express';
import { validateQuery } from '../middlewares/validation';
import { logger } from '../utils/logger';
import { z } from 'zod';
import { getInitializedScraper } from '../scraper/scraper-manager';
import { ConcurrentScraper } from '@/scraper/concurrent-scraper';

// Validation schemas
const FeedQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sources: z.string().optional(),
  type: z.enum(['all', 'curated', 'timeline', 'personalized']).default('all'),
  quality: z.enum(['all', 'high', 'trending']).default('all'),
});

const SearchQuerySchema = z.object({
  q: z.string().min(1, '搜索关键词不能为空'),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20),
  sources: z.string().optional(),
});

const router: Router = Router();

/**
 * @route GET /api/feed
 * @desc Get aggregated content feed with intelligent mixing
 * @access Public
 * @query {number} page - Page number
 * @query {number} limit - Items per page
 * @query {string} sources - Comma-separated source names
 * @query {string} quality - Content quality (all, high, trending)
 */
router.get('/', validateQuery(FeedQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { page, limit, sources, type, quality } = req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;

    let feedItems = await getAllFeed(concurrentScraper, targetSources, limit * 2);

    // Quality filtering
    if (quality === 'high') {
      feedItems = feedItems.filter(
        item =>
          (item.metadata?.importance || 0) > 5 ||
          (item.content?.length || 0) > 200 ||
          item.dataType === 'depth' ||
          item.dataType === 'column',
      );
    } else if (quality === 'trending') {
      feedItems = feedItems.filter(
        item =>
          (item.metadata?.likeCount || 0) > 10 ||
          (item.metadata?.readCount || 0) > 100 ||
          (item.metadata?.shareCount || 0) > 5,
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const paginatedItems = feedItems.slice(startIndex, startIndex + limit);

    // Enrich items
    const enrichedItems = paginatedItems.map(item => ({
      ...item,
      feedType: getFeedTypeForItem(item),
      weight: calculateFeedWeight(item),
      freshness: calculateFreshness(item.publishTime),
    }));

    const contentTypes = enrichedItems.reduce((acc: any, item: any) => {
      acc[item.dataType] = (acc[item.dataType] || 0) + 1;
      return acc;
    }, {});

    const sourceDistribution = enrichedItems.reduce((acc: any, item: any) => {
      acc[item.source] = (acc[item.source] || 0) + 1;
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        items: enrichedItems,
        pagination: {
          page,
          limit,
          total: feedItems.length,
          pages: Math.ceil(feedItems.length / limit),
          hasNext: startIndex + limit < feedItems.length,
          hasPrev: page > 1,
        },
        metadata: {
          feedType: type,
          qualityFilter: quality,
          contentTypeDistribution: contentTypes,
          sourceDistribution,
          algorithm: getFeedAlgorithmDescription(type),
          totalCandidates: feedItems.length,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取Feed数据失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取Feed数据失败',
    });
  }
});

/**
 * @route GET /api/feed/search
 * @desc Search in feed content
 * @access Public
 */
router.get('/search', validateQuery(SearchQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { q, page, limit, sources, type } = req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: q,
      sources: targetSources,
      limit: limit * 2,
      sortBy: 'publishTime',
    });

    // Filter by search type
    let filteredResults = searchResults.aggregatedResults;

    if (type === 'title') {
      filteredResults = searchResults.aggregatedResults.filter(item =>
        item.title.toLowerCase().includes(q.toLowerCase()),
      );
    } else if (type === 'author') {
      filteredResults = searchResults.aggregatedResults.filter(
        item => item.author && item.author.toLowerCase().includes(q.toLowerCase()),
      );
    }

    const startIndex = (page - 1) * limit;
    const paginatedResults = filteredResults.slice(startIndex, startIndex + limit);

    const enrichedResults = paginatedResults.map(item => ({
      ...item,
      relevanceScore: calculateRelevanceScore(item, q),
      matchType: getMatchType(item, q, type),
    }));

    res.json({
      success: true,
      data: {
        keyword: q,
        searchType: type,
        items: enrichedResults,
        pagination: {
          page,
          limit,
          total: filteredResults.length,
          pages: Math.ceil(filteredResults.length / limit),
          hasNext: startIndex + limit < filteredResults.length,
          hasPrev: page > 1,
        },
        metadata: {
          searchTime: searchResults.searchTime,
          totalMatches: filteredResults.length,
          sourceBreakdown: searchResults.sources,
        },
      },
    });
  } catch (error) {
    logger.error('Feed搜索失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || 'Feed搜索失败',
    });
  }
});

async function getAllFeed(
  concurrentScraper: ConcurrentScraper,
  sources: string[] | undefined,
  limit: number,
) {
  const results = await concurrentScraper.searchAcrossAllSources({
    keyword: '',
    sources,
    limit,
    sortBy: 'publishTime',
  });

  return results.aggregatedResults
    .map(item => ({
      ...item,
      feedScore: calculateFeedScore(item),
    }))
    .sort((a, b) => b.feedScore - a.feedScore);
}

function calculateFeedScore(item: any): number {
  const timeScore = calculateFreshness(item.publishTime) * 0.3;
  const qualityScore = ((item.content?.length || 0) / 1000) * 0.2;
  const interactionScore =
    ((item.metadata?.likeCount || 0) + (item.metadata?.readCount || 0) * 0.1) * 0.3;
  const typeScore = getTypeScore(item.dataType) * 0.2;

  return timeScore + qualityScore + interactionScore + typeScore;
}

function calculateFreshness(publishTime: string): number {
  const now = new Date().getTime();
  const pubTime = new Date(publishTime).getTime();
  const hoursAgo = (now - pubTime) / (1000 * 60 * 60);

  if (hoursAgo < 1) return 100;
  if (hoursAgo < 6) return 80;
  if (hoursAgo < 24) return 60;
  if (hoursAgo < 72) return 40;
  return 20;
}

function getTypeScore(dataType: string): number {
  const scores: Record<string, number> = {
    flash: 90,
    depth: 85,
    article: 80,
    column: 85,
    news: 75,
    post: 70,
    finance: 80,
    tweet: 60,
  };

  return scores[dataType] || 50;
}

function getFeedTypeForItem(item: any): string {
  if (item.dataType === 'flash') return 'breaking';
  if (['depth', 'column'].includes(item.dataType)) return 'analysis';
  if (['finance', 'financing'].includes(item.dataType)) return 'finance';
  if (item.dataType === 'tweet') return 'social';
  return 'news';
}

function calculateFeedWeight(item: any): number {
  return calculateFeedScore(item);
}

function calculateRelevanceScore(item: any, keyword: string): number {
  const titleMatch = item.title.toLowerCase().includes(keyword.toLowerCase()) ? 50 : 0;
  const contentMatch = item.content?.toLowerCase().includes(keyword.toLowerCase()) ? 30 : 0;
  const authorMatch = item.author?.toLowerCase().includes(keyword.toLowerCase()) ? 20 : 0;

  return titleMatch + contentMatch + authorMatch;
}

function getMatchType(item: any, keyword: string, searchType: string): string {
  if (searchType === 'title' || item.title.toLowerCase().includes(keyword.toLowerCase())) {
    return 'title';
  }
  if (searchType === 'author' || item.author?.toLowerCase().includes(keyword.toLowerCase())) {
    return 'author';
  }
  return 'content';
}

function getFeedAlgorithmDescription(type: string): string {
  const descriptions: Record<string, string> = {
    all: 'Multi-factor scoring: freshness(30%) + quality(20%) + interaction(30%) + type(20%)',
    curated: 'Quality-first: content length + importance + editorial selection',
    timeline: 'Chronological ordering with recency boost',
    personalized: 'User-preference based ML recommendation (simplified)',
  };

  return descriptions[type] || descriptions['all'];
}

export default router;
