import { Router, Request, Response } from 'express';
import { validateQuery } from '../middlewares/validation';
import { logger } from '../utils/logger';
import { z } from 'zod';
import { getInitializedScraper } from '../scraper/scraper-manager';

// Validation schemas
const FinancingQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sources: z.string().optional(),
  sort: z.enum(['publishTime', 'amount', 'relevance']).default('publishTime'),
  order: z.enum(['asc', 'desc']).default('desc'),
  round: z.string().optional(), // 融资轮次筛选
  minAmount: z.coerce.number().optional(), // 最小金额
  maxAmount: z.coerce.number().optional(), // 最大金额
});

const TrendingFinancingQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(50).default(10),
});

const router: Router = Router();

/**
 * @route GET /api/financing
 * @desc Get financing information from all sources
 * @access Public
 */
router.get('/', validateQuery(FinancingQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { page, limit, sources, sort, order, round, minAmount, maxAmount } = req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      sources: targetSources,
      dataTypes: ['finance', 'financing', 'fundraising'],
      limit: limit * 2,
      sortBy: 'publishTime',
    });

    let financingItems = searchResults.aggregatedResults.map(item => ({
      ...item,
      financingScore: calculateFinancingScore(item),
      parsedAmount: parseFinancingAmount(item),
      parsedRound: parseFinancingRound(item),
    }));

    // 应用筛选条件
    if (round) {
      financingItems = financingItems.filter(item =>
        item.parsedRound?.toLowerCase().includes(round.toLowerCase()),
      );
    }

    if (minAmount !== undefined) {
      financingItems = financingItems.filter(
        item => item.parsedAmount && item.parsedAmount >= minAmount,
      );
    }

    if (maxAmount !== undefined) {
      financingItems = financingItems.filter(
        item => item.parsedAmount && item.parsedAmount <= maxAmount,
      );
    }

    // 排序
    financingItems.sort((a, b) => {
      let aVal, bVal;
      switch (sort) {
        case 'amount':
          aVal = a.parsedAmount || 0;
          bVal = b.parsedAmount || 0;
          break;
        case 'relevance':
          aVal = a.financingScore;
          bVal = b.financingScore;
          break;
        default: // publishTime
          aVal = new Date(a.publishTime).getTime();
          bVal = new Date(b.publishTime).getTime();
      }
      return order === 'asc' ? aVal - bVal : bVal - aVal;
    });

    // 分页
    const startIndex = (page - 1) * limit;
    const paginatedItems = financingItems.slice(startIndex, startIndex + limit);

    // 统计信息
    const stats = calculateFinancingStats(financingItems);

    res.json({
      success: true,
      data: {
        items: paginatedItems,
        pagination: {
          page,
          limit,
          total: financingItems.length,
          pages: Math.ceil(financingItems.length / limit),
          hasNext: startIndex + limit < financingItems.length,
          hasPrev: page > 1,
        },
        metadata: {
          sortBy: sort,
          sortOrder: order,
          filters: { round, minAmount, maxAmount },
          stats,
          sourceBreakdown: searchResults.sources,
          totalSources: Object.keys(searchResults.sources).length,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取融资信息失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取融资信息失败',
    });
  }
});

/**
 * @route GET /api/financing/trending
 * @desc Get trending financing deals
 * @access Public
 */
router.get(
  '/trending',
  validateQuery(TrendingFinancingQuerySchema),
  async (req: Request, res: Response) => {
    try {
      const concurrentScraper = await getInitializedScraper();

      const { limit } = req.query as any;

      const searchResults = await concurrentScraper.searchAcrossAllSources({
        keyword: '',
        dataTypes: ['finance', 'financing', 'fundraising'],
        limit: limit * 3,
        sortBy: 'publishTime',
      });

      const trendingDeals = searchResults.aggregatedResults
        .map(item => ({
          ...item,
          financingScore: calculateFinancingScore(item),
          parsedAmount: parseFinancingAmount(item),
          parsedRound: parseFinancingRound(item),
          trendingScore: calculateTrendingScore(item),
        }))
        .sort((a, b) => b.trendingScore - a.trendingScore)
        .slice(0, limit);

      res.json({
        success: true,
        data: {
          items: trendingDeals,
          metadata: {
            algorithm: 'trendingScore: amount_weight + recency + engagement',
            totalCandidates: searchResults.totalResults,
            averageTrendingScore:
              trendingDeals.reduce((sum, item) => sum + item.trendingScore, 0) /
                trendingDeals.length || 0,
            generatedAt: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      logger.error('获取热门融资信息失败:', error);
      res.status(500).json({
        success: false,
        error: (error as any)?.message || '获取热门融资信息失败',
      });
    }
  },
);

// Helper functions

function calculateFinancingScore(item: any): number {
  let score = 10; // Base score

  // Amount factor
  const amount = parseFinancingAmount(item);
  if (amount) {
    if (amount >= 100000000)
      score += 30; // 1亿+
    else if (amount >= 10000000)
      score += 20; // 1000万+
    else if (amount >= 1000000) score += 10; // 100万+
  }

  // Round factor
  const round = parseFinancingRound(item);
  if (round) {
    if (round.includes('A轮') || round.includes('Series A')) score += 15;
    else if (round.includes('B轮') || round.includes('Series B')) score += 20;
    else if (round.includes('C轮') || round.includes('Series C')) score += 25;
    else if (round.includes('种子') || round.includes('Seed')) score += 10;
  }

  // Recency factor
  const hoursAgo = (new Date().getTime() - new Date(item.publishTime).getTime()) / (1000 * 60 * 60);
  if (hoursAgo < 6) score += 15;
  else if (hoursAgo < 24) score += 10;
  else if (hoursAgo < 72) score += 5;

  return Math.min(score, 100);
}

function parseFinancingAmount(item: any): number | null {
  const text = `${item.title} ${item.content}`.toLowerCase();

  // 匹配金额模式
  const patterns = [
    /(\d+(?:\.\d+)?)\s*亿\s*美元/g,
    /(\d+(?:\.\d+)?)\s*亿\s*元/g,
    /(\d+(?:\.\d+)?)\s*万\s*美元/g,
    /(\d+(?:\.\d+)?)\s*万\s*元/g,
    /\$(\d+(?:\.\d+)?)\s*[mM]/g, // $10M
    /\$(\d+(?:\.\d+)?)\s*[bB]/g, // $1B
  ];

  for (const pattern of patterns) {
    const match = pattern.exec(text);
    if (match) {
      const amount = parseFloat(match[1]);
      if (text.includes('亿美元')) return amount * 100000000;
      if (text.includes('亿元')) return amount * 100000000 * 0.14; // 假设汇率
      if (text.includes('万美元')) return amount * 10000;
      if (text.includes('万元')) return amount * 10000 * 0.14;
      if (text.includes('m') || text.includes('M')) return amount * 1000000;
      if (text.includes('b') || text.includes('B')) return amount * 1000000000;
    }
  }

  return null;
}

function parseFinancingRound(item: any): string | null {
  const text = `${item.title} ${item.content}`;

  const rounds = [
    '种子轮',
    'Seed',
    'Pre-A',
    'A轮',
    'Series A',
    'A+轮',
    'B轮',
    'Series B',
    'B+轮',
    'C轮',
    'Series C',
    'D轮',
    'Series D',
    '战略投资',
    'IPO',
  ];

  for (const round of rounds) {
    if (text.includes(round)) {
      return round;
    }
  }

  return null;
}

function calculateTrendingScore(item: any): number {
  const financingScore = calculateFinancingScore(item);
  const amount = parseFinancingAmount(item) || 0;
  const amountWeight = Math.min(amount / 10000000, 50); // 最多50分

  return financingScore + amountWeight;
}

function calculateFinancingStats(items: any[]): any {
  const totalAmount = items.reduce((sum, item) => sum + (item.parsedAmount || 0), 0);
  const avgAmount = totalAmount / items.filter(item => item.parsedAmount).length || 0;

  const roundDistribution = items.reduce((acc: any, item: any) => {
    const round = item.parsedRound || '未知';
    acc[round] = (acc[round] || 0) + 1;
    return acc;
  }, {});

  return {
    totalDeals: items.length,
    totalAmount,
    averageAmount: avgAmount,
    roundDistribution,
    topRounds: Object.entries(roundDistribution)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 5),
  };
}

export default router;
