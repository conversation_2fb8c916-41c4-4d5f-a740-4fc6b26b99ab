import { Router, Request, Response } from 'express';
import { validateQuery } from '../middlewares/validation';
import { logger } from '../utils/logger';
import { z } from 'zod';
import { getInitializedScraper } from '../scraper/scraper-manager';

// Validation schemas
const FlashQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sources: z.string().optional(),
  priority: z.enum(['all', 'high', 'urgent', 'breaking']).default('all'),
  category: z.string().optional(),
  realtime: z.coerce.boolean().default(false),
});

const SearchQuerySchema = z.object({
  q: z.string().min(1, '搜索关键词不能为空'),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20),
  sources: z.string().optional(),
});

const router: Router = Router();

/**
 * @route GET /api/flash
 * @desc Get real-time flash news from all sources
 * @access Public
 * @query {number} page - Page number
 * @query {number} limit - Items per page
 * @query {string} sources - Comma-separated source names
 * @query {string} priority - Priority filter (all, high, urgent, breaking)
 * @query {string} category - Category filter
 * @query {boolean} realtime - Real-time mode
 */
router.get('/', validateQuery(FlashQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { page, limit, sources, priority, category, realtime } = req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: category || '',
      sources: targetSources,
      dataTypes: ['flash'],
      limit: limit * 3,
      sortBy: 'publishTime',
    });

    let flashItems = searchResults.aggregatedResults.map(item => ({
      ...item,
      urgencyScore: calculateUrgencyScore(item),
      priority: calculatePriority(item),
      freshness: calculateFreshness(item.publishTime),
      isBreaking: isBreakingNews(item),
    }));

    // Priority filtering
    if (priority === 'high') {
      flashItems = flashItems.filter(item => item.urgencyScore >= 60);
    } else if (priority === 'urgent') {
      flashItems = flashItems.filter(item => item.urgencyScore >= 80);
    } else if (priority === 'breaking') {
      flashItems = flashItems.filter(item => item.isBreaking);
    }

    // Sorting
    flashItems.sort((a, b) => {
      if (realtime) {
        return new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime();
      } else {
        return b.urgencyScore + b.freshness - (a.urgencyScore + a.freshness);
      }
    });

    const startIndex = (page - 1) * limit;
    const paginatedItems = flashItems.slice(startIndex, startIndex + limit);

    const priorityDistribution = flashItems.reduce((acc: any, item: any) => {
      acc[item.priority] = (acc[item.priority] || 0) + 1;
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        items: paginatedItems,
        pagination: {
          page,
          limit,
          total: flashItems.length,
          pages: Math.ceil(flashItems.length / limit),
          hasNext: startIndex + limit < flashItems.length,
          hasPrev: page > 1,
        },
        metadata: {
          priorityFilter: priority,
          priorityDistribution,
          realtimeMode: realtime,
          breakingCount: flashItems.filter(item => item.isBreaking).length,
          totalCandidates: searchResults.totalResults,
          averageUrgency:
            flashItems.reduce((sum, item) => sum + item.urgencyScore, 0) / flashItems.length || 0,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取快讯失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取快讯失败',
    });
  }
});

/**
 * @route GET /api/flash/search
 * @desc Search flash news items
 * @access Public
 */
router.get('/search', validateQuery(SearchQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { q, page, limit, sources } = req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: q,
      sources: targetSources,
      dataTypes: ['flash'],
      limit: limit * 2,
      sortBy: 'publishTime',
    });

    const filteredResults = searchResults.aggregatedResults
      .map(item => ({
        ...item,
        relevanceScore: calculateRelevanceScore(item, q),
        urgencyScore: calculateUrgencyScore(item),
        isBreaking: isBreakingNews(item),
      }))
      .sort((a, b) => b.relevanceScore + b.urgencyScore - (a.relevanceScore + a.urgencyScore));

    const startIndex = (page - 1) * limit;
    const paginatedResults = filteredResults.slice(startIndex, startIndex + limit);

    res.json({
      success: true,
      data: {
        keyword: q,
        items: paginatedResults,
        pagination: {
          page,
          limit,
          total: filteredResults.length,
          pages: Math.ceil(filteredResults.length / limit),
          hasNext: startIndex + limit < filteredResults.length,
          hasPrev: page > 1,
        },
        metadata: {
          searchTime: searchResults.searchTime,
          breakingCount: filteredResults.filter(item => item.isBreaking).length,
          averageRelevance:
            filteredResults.reduce((sum, item) => sum + item.relevanceScore, 0) /
              filteredResults.length || 0,
          sourceBreakdown: searchResults.sources,
        },
      },
    });
  } catch (error) {
    logger.error('搜索快讯失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '搜索快讯失败',
    });
  }
});

// Helper functions

function calculateUrgencyScore(item: any): number {
  let score = 50; // Base score

  // Time factor - recent news gets higher score
  const hoursAgo = (new Date().getTime() - new Date(item.publishTime).getTime()) / (1000 * 60 * 60);
  if (hoursAgo < 0.5)
    score += 30; // Within 30 minutes
  else if (hoursAgo < 1)
    score += 20; // Within 1 hour
  else if (hoursAgo < 6) score += 10; // Within 6 hours

  // Content keywords that indicate urgency
  const urgentKeywords = ['突发', '紧急', '重要', '重大', '破纪录', '暴跌', '暴涨', '停止', '危机'];
  const titleLower = item.title.toLowerCase();
  urgentKeywords.forEach(keyword => {
    if (titleLower.includes(keyword)) score += 15;
  });

  // Interaction metrics
  if (item.metadata?.likeCount) score += Math.min(item.metadata.likeCount * 0.1, 10);
  if (item.metadata?.shareCount) score += Math.min(item.metadata.shareCount * 0.5, 15);

  return Math.min(score, 100);
}

function calculatePriority(item: any): string {
  const urgencyScore = item.urgencyScore || calculateUrgencyScore(item);

  if (urgencyScore >= 85) return 'breaking';
  if (urgencyScore >= 70) return 'urgent';
  if (urgencyScore >= 55) return 'high';
  return 'normal';
}

function calculateFreshness(publishTime: string): number {
  const now = new Date().getTime();
  const pubTime = new Date(publishTime).getTime();
  const minutesAgo = (now - pubTime) / (1000 * 60);

  if (minutesAgo < 15) return 100;
  if (minutesAgo < 60) return 80;
  if (minutesAgo < 360) return 60; // 6 hours
  if (minutesAgo < 1440) return 40; // 24 hours
  return 20;
}

function isBreakingNews(item: any): boolean {
  const urgencyScore = item.urgencyScore || calculateUrgencyScore(item);
  const freshness = calculateFreshness(item.publishTime);

  return urgencyScore >= 80 && freshness >= 60;
}

function calculateRelevanceScore(item: any, keyword: string): number {
  const titleMatch = item.title.toLowerCase().includes(keyword.toLowerCase()) ? 50 : 0;
  const contentMatch = item.content?.toLowerCase().includes(keyword.toLowerCase()) ? 30 : 0;
  const tagMatch = item.tags?.some((tag: string) =>
    tag.toLowerCase().includes(keyword.toLowerCase()),
  )
    ? 20
    : 0;

  return titleMatch + contentMatch + tagMatch;
}

export default router;
