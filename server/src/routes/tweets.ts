import { Router, Request, Response } from 'express';
import { validateQuery } from '../middlewares/validation';
import { logger } from '../utils/logger';
import { z } from 'zod';
import { getInitializedScraper } from '../scraper/scraper-manager';

// Validation schemas
const TweetsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sources: z.string().optional(),
  sort: z.enum(['publishTime', 'popularity', 'relevance']).default('publishTime'),
  order: z.enum(['asc', 'desc']).default('desc'),
});

const HotTweetsQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(50).default(10),
});

const router: Router = Router();

/**
 * @route GET /api/tweets/hot
 * @desc Get hot tweets from all sources
 * @access Public
 * @query {number} limit - Number of tweets to return
 */
router.get('/hot', validateQuery(HotTweetsQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { limit } = req.query as any;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      dataTypes: ['tweet'],
      limit: limit * 3,
      sortBy: 'publishTime',
    });

    let hotTweets = searchResults.aggregatedResults
      .map(item => ({
        ...item,
        hotScore: calculateTweetHotScore(item),
        engagement: {
          likes: item.metadata?.likeCount || 0,
          shares: item.metadata?.shareCount || 0,
          replies: item.metadata?.replyCount || 0,
        },
      }))
      .sort((a, b) => b.hotScore - a.hotScore)
      .slice(0, limit);

    res.json({
      success: true,
      data: { items: hotTweets },
      metadata: {
        algorithm: 'hotScore: recency + engagement + content_quality',
        totalCandidates: searchResults.totalResults,
        averageHotScore:
          hotTweets.reduce((sum, item) => sum + item.hotScore, 0) / hotTweets.length || 0,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('获取热门推特失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取热门推特失败',
    });
  }
});

/**
 * @route GET /api/tweets
 * @desc Get tweets from all sources with filtering and sorting
 * @access Public
 */
router.get('/', validateQuery(TweetsQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { page, limit, sources, sort, order } = req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      sources: targetSources,
      dataTypes: ['tweet'],
      limit: limit * 2,
      sortBy: 'publishTime',
    });

    let tweets = searchResults.aggregatedResults.map(item => ({
      ...item,
      hotScore: calculateTweetHotScore(item),
      engagement: {
        likes: item.metadata?.likeCount || 0,
        shares: item.metadata?.shareCount || 0,
        replies: item.metadata?.replyCount || 0,
      },
    }));

    // 排序
    tweets.sort((a, b) => {
      let aVal, bVal;
      switch (sort) {
        case 'popularity':
          aVal = a.hotScore;
          bVal = b.hotScore;
          break;
        case 'relevance':
          aVal = a.hotScore + a.engagement.likes * 0.1;
          bVal = b.hotScore + b.engagement.likes * 0.1;
          break;
        default: // publishTime
          aVal = new Date(a.publishTime).getTime();
          bVal = new Date(b.publishTime).getTime();
      }
      return order === 'asc' ? aVal - bVal : bVal - aVal;
    });

    // 分页
    const startIndex = (page - 1) * limit;
    const paginatedTweets = tweets.slice(startIndex, startIndex + limit);

    res.json({
      success: true,
      data: {
        items: paginatedTweets,
        pagination: {
          page,
          limit,
          total: tweets.length,
          pages: Math.ceil(tweets.length / limit),
          hasNext: startIndex + limit < tweets.length,
          hasPrev: page > 1,
        },
        metadata: {
          sortBy: sort,
          sortOrder: order,
          sourceBreakdown: searchResults.sources,
          totalSources: Object.keys(searchResults.sources).length,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取推特列表失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取推特列表失败',
    });
  }
});

/**
 * @route GET /api/tweets/by-source/:source
 * @desc Get tweets from specific source
 * @access Public
 */
router.get(
  '/by-source/:source',
  validateQuery(TweetsQuerySchema),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const concurrentScraper = await getInitializedScraper();

      const { source } = req.params;
      const { page, limit } = req.query as any;

      const searchResults = await concurrentScraper.searchAcrossAllSources({
        keyword: '',
        sources: [source],
        dataTypes: ['tweet'],
        limit: limit * 2,
        sortBy: 'publishTime',
      });

      const tweets = searchResults.aggregatedResults
        .map(item => ({
          ...item,
          hotScore: calculateTweetHotScore(item),
          engagement: {
            likes: item.metadata?.likeCount || 0,
            shares: item.metadata?.shareCount || 0,
            replies: item.metadata?.replyCount || 0,
          },
        }))
        .sort((a, b) => new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime());

      const startIndex = (page - 1) * limit;
      const paginatedTweets = tweets.slice(startIndex, startIndex + limit);

      res.json({
        success: true,
        data: {
          source,
          items: paginatedTweets,
          pagination: {
            page,
            limit,
            total: tweets.length,
            pages: Math.ceil(tweets.length / limit),
            hasNext: startIndex + limit < tweets.length,
            hasPrev: page > 1,
          },
        },
      });
    } catch (error) {
      logger.error(`获取数据源 ${req.params.source} 推特失败:`, error);
      res.status(500).json({
        success: false,
        error: (error as any)?.message || '获取数据源推特失败',
      });
    }
  },
);

// Helper function to calculate tweet hot score
function calculateTweetHotScore(item: any): number {
  let score = 10; // Base score

  // Recency factor
  const hoursAgo = (new Date().getTime() - new Date(item.publishTime).getTime()) / (1000 * 60 * 60);
  if (hoursAgo < 1) score += 30;
  else if (hoursAgo < 6) score += 20;
  else if (hoursAgo < 24) score += 10;

  // Engagement metrics
  score += (item.metadata?.likeCount || 0) * 0.5;
  score += (item.metadata?.shareCount || 0) * 1.0;
  score += (item.metadata?.replyCount || 0) * 0.3;

  // Content quality indicators
  if (item.tweetText && item.tweetText.length > 100) score += 5;
  if (item.tags && item.tags.length > 0) score += item.tags.length * 2;

  // Author influence (if available)
  if (item.metadata?.followerCount) {
    score += Math.min(item.metadata.followerCount / 10000, 20);
  }

  return Math.min(score, 100);
}

export default router;
