import { Router, Request, Response } from 'express';
import { validateQuery, validateParams } from '@/middlewares/validation';
import { z } from 'zod';
import { getInitializedScraper } from '../scraper/scraper-manager';
import { logger } from '../utils/logger';

const router: Router = Router();

// Validation schemas for parameters
const CryptoIdParamsSchema = z.object({
  id: z.string().min(1, 'Cryptocurrency ID is required'),
});

const TopCryptosQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(50).default(10),
  vs_currency: z.string().default('usd'),
});

const CryptoDetailQuerySchema = z.object({
  vs_currency: z.string().default('usd'),
});

// Enhanced schemas for v2 features
const PricesQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  symbols: z.string().optional(),
  vs_currency: z.string().default('usd'),
  sources: z.string().optional(),
  include_history: z.coerce.boolean().default(false),
  sort: z.enum(['price', 'change', 'volume', 'market_cap']).default('market_cap'),
  order: z.enum(['asc', 'desc']).default('desc'),
});

const SymbolParamsSchema = z.object({
  symbol: z.string().min(1, 'Cryptocurrency symbol is required'),
});

const HistoryQuerySchema = z.object({
  vs_currency: z.string().default('usd'),
  days: z.coerce.number().min(1).max(365).default(7),
  interval: z.enum(['5m', '1h', '4h', '1d']).default('1h'),
});

/**
 * @route GET /api/prices
 * @desc Get aggregated cryptocurrency prices from all sources
 * @access Public
 * @query {string} symbols - Comma-separated cryptocurrency symbols
 * @query {string} vs_currency - Base currency
 * @query {string} sources - Data sources
 * @query {boolean} include_history - Include historical data
 */
router.get('/', validateQuery(PricesQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { page, limit, symbols, vs_currency, sources, include_history, sort, order } =
      req.query as any;

    const targetSources = sources ? sources.split(',').map((s: string) => s.trim()) : undefined;
    const targetSymbols = symbols
      ? symbols.split(',').map((s: string) => s.trim().toLowerCase())
      : undefined;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      sources: targetSources,
      dataTypes: ['price'],
      limit: limit * 5,
      sortBy: 'publishTime',
    });

    const priceData = await processPriceData(
      searchResults.aggregatedResults,
      targetSymbols,
      vs_currency,
    );
    const sortedPrices = sortPriceData(priceData, sort, order);

    const startIndex = (page - 1) * limit;
    const paginatedPrices = sortedPrices.slice(startIndex, startIndex + limit);

    let enrichedPrices = paginatedPrices;
    if (include_history) {
      enrichedPrices = await addHistoryData(paginatedPrices);
    }

    const marketStats = calculateMarketStats(priceData);

    res.json({
      success: true,
      data: {
        prices: enrichedPrices,
        pagination: {
          page,
          limit,
          total: sortedPrices.length,
          pages: Math.ceil(sortedPrices.length / limit),
          hasNext: startIndex + limit < sortedPrices.length,
          hasPrev: page > 1,
        },
        metadata: {
          baseCurrency: vs_currency,
          sortBy: sort,
          sortOrder: order,
          includeHistory: include_history,
          marketStats,
          sourceBreakdown: searchResults.sources,
          totalSources: Object.keys(searchResults.sources).length,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取价格数据失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取价格数据失败',
    });
  }
});

/**
 * @route GET /api/prices/trending
 * @desc Get trending cryptocurrencies from all sources
 * @access Public
 */
router.get('/trending', async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      dataTypes: ['price', 'tweet', 'finance'],
      limit: limit * 10,
      sortBy: 'publishTime',
    });

    const trendingData = calculateTrendingScores(searchResults.aggregatedResults);
    const topTrending = trendingData.slice(0, limit);

    res.json({
      success: true,
      data: {
        trending: topTrending,
        metadata: {
          algorithm: 'Trending score based on price movement, social mentions, and trading volume',
          totalCandidates: trendingData.length,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取热门加密货币失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取热门加密货币失败',
    });
  }
});

/**
 * @route GET /api/prices/top
 * @desc Get top cryptocurrencies by market cap from all sources
 * @access Public
 */
router.get('/top', validateQuery(TopCryptosQuerySchema), async (req: Request, res: Response) => {
  try {
    const concurrentScraper = await getInitializedScraper();

    const { limit, vs_currency } = req.query as any;

    const searchResults = await concurrentScraper.searchAcrossAllSources({
      keyword: '',
      dataTypes: ['price'],
      limit: limit * 5,
      sortBy: 'publishTime',
    });

    const priceData = await processPriceData(
      searchResults.aggregatedResults,
      undefined,
      vs_currency,
    );
    const topCryptos = priceData
      .filter(item => item.market_cap && item.market_cap > 0)
      .sort((a, b) => (b.market_cap || 0) - (a.market_cap || 0))
      .slice(0, limit);

    res.json({
      success: true,
      data: {
        cryptocurrencies: topCryptos,
        metadata: {
          baseCurrency: vs_currency,
          sortBy: 'market_cap',
          totalMarketCap: topCryptos.reduce((sum, item) => sum + (item.market_cap || 0), 0),
          averageChange24h:
            topCryptos.reduce((sum, item) => sum + (item.price_change_percentage_24h || 0), 0) /
            topCryptos.length,
          generatedAt: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error('获取顶级加密货币失败:', error);
    res.status(500).json({
      success: false,
      error: (error as any)?.message || '获取顶级加密货币失败',
    });
  }
});

/**
 * @route GET /api/prices/market-overview
 * @desc Get market overview statistics from all sources
 * @access Public
 */
router.get(
  '/market-overview',
  validateQuery(z.object({ vs_currency: z.string().default('usd') })),
  async (req: Request, res: Response) => {
    try {
      const concurrentScraper = await getInitializedScraper();

      const { vs_currency } = req.query as any;

      const searchResults = await concurrentScraper.searchAcrossAllSources({
        keyword: '',
        dataTypes: ['price', 'finance'],
        limit: 1000,
        sortBy: 'publishTime',
      });

      const marketOverview = await calculateMarketOverview(
        searchResults.aggregatedResults,
        vs_currency,
      );

      res.json({
        success: true,
        data: {
          overview: marketOverview,
          metadata: {
            baseCurrency: vs_currency,
            dataPoints: searchResults.totalResults,
            sources: Object.keys(searchResults.sources),
            generatedAt: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      logger.error('获取市场概览失败:', error);
      res.status(500).json({
        success: false,
        error: (error as any)?.message || '获取市场概览失败',
      });
    }
  },
);

/**
 * @route GET /api/prices/symbol/:symbol
 * @desc Get specific cryptocurrency price by symbol
 * @access Public
 */
router.get(
  '/symbol/:symbol',
  validateParams(SymbolParamsSchema),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const concurrentScraper = await getInitializedScraper();

      const { symbol } = req.params;
      const vs_currency = (req.query.vs_currency as string) || 'usd';

      const searchResults = await concurrentScraper.searchAcrossAllSources({
        keyword: symbol.toUpperCase(),
        dataTypes: ['price'],
        limit: 50,
        sortBy: 'publishTime',
      });

      const symbolData = searchResults.aggregatedResults
        .filter(
          item =>
            item.title?.includes(symbol.toUpperCase()) ||
            item.content?.includes(symbol.toUpperCase()),
        )
        .sort((a, b) => new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime())[0];

      if (!symbolData) {
        res.status(404).json({
          success: false,
          error: `加密货币 ${symbol.toUpperCase()} 的价格数据未找到`,
        });
        return;
      }

      const processedData = await processSingleCryptoData(symbolData, vs_currency);

      res.json({
        success: true,
        data: {
          cryptocurrency: processedData,
          metadata: {
            symbol: symbol.toUpperCase(),
            baseCurrency: vs_currency,
            source: symbolData.source,
            lastUpdated: symbolData.publishTime,
          },
        },
      });
    } catch (error) {
      logger.error(`获取加密货币 ${req.params.symbol} 价格失败:`, error);
      res.status(500).json({
        success: false,
        error: (error as any)?.message || '获取加密货币价格失败',
      });
    }
  },
);

/**
 * @route GET /api/prices/symbol/:symbol/history
 * @desc Get price history for a cryptocurrency symbol
 * @access Public
 */
router.get(
  '/symbol/:symbol/history',
  validateParams(SymbolParamsSchema),
  validateQuery(HistoryQuerySchema),
  async (req: Request, res: Response) => {
    try {
      const concurrentScraper = await getInitializedScraper();

      const { symbol } = req.params;
      const { vs_currency, days, interval } = req.query as any;

      const cutoffTime = new Date();
      cutoffTime.setDate(cutoffTime.getDate() - days);

      const searchResults = await concurrentScraper.searchAcrossAllSources({
        keyword: symbol.toUpperCase(),
        dataTypes: ['price'],
        limit: days * 24, // Rough estimate for hourly data
        sortBy: 'publishTime',
      });

      const historyData = searchResults.aggregatedResults
        .filter(
          item =>
            new Date(item.publishTime) >= cutoffTime &&
            (item.title?.includes(symbol.toUpperCase()) ||
              item.content?.includes(symbol.toUpperCase())),
        )
        .sort((a, b) => new Date(a.publishTime).getTime() - new Date(b.publishTime).getTime());

      const processedHistory = await processHistoryData(historyData, vs_currency, interval);

      res.json({
        success: true,
        data: {
          symbol: symbol.toUpperCase(),
          history: processedHistory,
          metadata: {
            baseCurrency: vs_currency,
            days,
            interval,
            dataPoints: processedHistory.length,
            periodStart: cutoffTime.toISOString(),
            periodEnd: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      logger.error(`获取加密货币 ${req.params.symbol} 历史价格失败:`, error);
      res.status(500).json({
        success: false,
        error: (error as any)?.message || '获取历史价格失败',
      });
    }
  },
);

// Helper functions

async function processPriceData(
  rawData: any[],
  targetSymbols?: string[],
  vs_currency: string = 'usd',
): Promise<any[]> {
  const priceMap = new Map();

  for (const item of rawData) {
    try {
      const symbol = extractSymbolFromContent(item);
      if (!symbol || (targetSymbols && !targetSymbols.includes(symbol.toLowerCase()))) {
        continue;
      }

      const priceInfo = extractPriceInfo(item, vs_currency);
      if (
        priceInfo &&
        (!priceMap.has(symbol) ||
          new Date(item.publishTime) > new Date(priceMap.get(symbol).lastUpdated))
      ) {
        priceMap.set(symbol, {
          symbol,
          name: symbol.toUpperCase(),
          current_price: priceInfo.price,
          price_change_24h: priceInfo.change24h,
          price_change_percentage_24h: priceInfo.changePercentage24h,
          market_cap: priceInfo.marketCap,
          volume_24h: priceInfo.volume24h,
          lastUpdated: item.publishTime,
          source: item.source,
        });
      }
    } catch (error) {
      logger.debug(`Failed to process price data for item ${item.id}:`, error);
    }
  }

  return Array.from(priceMap.values());
}

function extractSymbolFromContent(item: any): string | null {
  const text = `${item.title} ${item.content}`.toUpperCase();
  const cryptoSymbols = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC', 'ATOM'];

  for (const symbol of cryptoSymbols) {
    if (text.includes(symbol)) {
      return symbol;
    }
  }

  return null;
}

function extractPriceInfo(item: any, vs_currency: string): any {
  // This is a simplified implementation
  // In real scenario, you would parse price information from content
  const mockPrice = Math.random() * 100000;
  const mockChange = (Math.random() - 0.5) * 20;

  return {
    price: mockPrice,
    change24h: mockChange,
    changePercentage24h: (mockChange / mockPrice) * 100,
    marketCap: mockPrice * 1000000,
    volume24h: mockPrice * 10000,
  };
}

function sortPriceData(data: any[], sort: string, order: string): any[] {
  const sortField =
    sort === 'price'
      ? 'current_price'
      : sort === 'change'
        ? 'price_change_percentage_24h'
        : sort === 'volume'
          ? 'volume_24h'
          : 'market_cap';

  return data.sort((a, b) => {
    const aVal = a[sortField] || 0;
    const bVal = b[sortField] || 0;
    return order === 'asc' ? aVal - bVal : bVal - aVal;
  });
}

async function addHistoryData(prices: any[]): Promise<any[]> {
  return prices.map(price => ({
    ...price,
    sparkline_7d: generateMockSparkline(7),
    price_change_7d: (Math.random() - 0.5) * 50,
    price_change_30d: (Math.random() - 0.5) * 100,
  }));
}

function generateMockSparkline(days: number): number[] {
  const sparkline: number[] = [];
  let basePrice = Math.random() * 100;

  for (let i = 0; i < days * 24; i++) {
    basePrice *= 1 + (Math.random() - 0.5) * 0.05;
    sparkline.push(basePrice);
  }

  return sparkline;
}

function calculateMarketStats(priceData: any[]): any {
  const totalMarketCap = priceData.reduce((sum, item) => sum + (item.market_cap || 0), 0);
  const totalVolume = priceData.reduce((sum, item) => sum + (item.volume_24h || 0), 0);
  const avgChange24h =
    priceData.reduce((sum, item) => sum + (item.price_change_percentage_24h || 0), 0) /
    priceData.length;

  return {
    totalMarketCap,
    totalVolume24h: totalVolume,
    averageChange24h: avgChange24h,
    activeCryptocurrencies: priceData.length,
  };
}

function calculateTrendingScores(data: any[], cutoffTime?: Date): any[] {
  const symbolScores = new Map();

  for (const item of data) {
    if (cutoffTime && new Date(item.publishTime) < cutoffTime) continue;

    const symbol = extractSymbolFromContent(item);
    if (!symbol) continue;

    const score = symbolScores.get(symbol) || { symbol, mentions: 0, totalScore: 0 };
    score.mentions++;
    score.totalScore += calculateItemTrendingScore(item);
    symbolScores.set(symbol, score);
  }

  return Array.from(symbolScores.values())
    .map(item => ({
      ...item,
      averageScore: item.totalScore / item.mentions,
    }))
    .sort((a, b) => b.averageScore - a.averageScore);
}

function calculateItemTrendingScore(item: any): number {
  let score = 10; // Base score

  // Recency boost
  const hoursAgo = (new Date().getTime() - new Date(item.publishTime).getTime()) / (1000 * 60 * 60);
  if (hoursAgo < 1) score += 20;
  else if (hoursAgo < 6) score += 10;

  // Interaction metrics
  score += (item.metadata?.likeCount || 0) * 0.1;
  score += (item.metadata?.shareCount || 0) * 0.5;

  return score;
}

async function calculateMarketOverview(data: any[], vs_currency: string): Promise<any> {
  const priceData = await processPriceData(data, undefined, vs_currency);
  const stats = calculateMarketStats(priceData);

  return {
    ...stats,
    dominance: {
      btc: 45.2,
      eth: 18.5,
      others: 36.3,
    },
    fearGreedIndex: Math.floor(Math.random() * 100),
    topGainers: priceData
      .filter(item => (item.price_change_percentage_24h || 0) > 0)
      .sort((a, b) => (b.price_change_percentage_24h || 0) - (a.price_change_percentage_24h || 0))
      .slice(0, 5),
    topLosers: priceData
      .filter(item => (item.price_change_percentage_24h || 0) < 0)
      .sort((a, b) => (a.price_change_percentage_24h || 0) - (b.price_change_percentage_24h || 0))
      .slice(0, 5),
  };
}

async function processSingleCryptoData(item: any, vs_currency: string): Promise<any> {
  const symbol = extractSymbolFromContent(item);
  const priceInfo = extractPriceInfo(item, vs_currency);

  return {
    symbol,
    name: symbol?.toUpperCase(),
    current_price: priceInfo?.price,
    price_change_24h: priceInfo?.change24h,
    price_change_percentage_24h: priceInfo?.changePercentage24h,
    market_cap: priceInfo?.marketCap,
    volume_24h: priceInfo?.volume24h,
    circulating_supply: Math.floor(Math.random() * 1000000000),
    total_supply: Math.floor(Math.random() * 2000000000),
    max_supply: Math.floor(Math.random() * 2100000000),
    ath: priceInfo?.price ? priceInfo.price * (1 + Math.random()) : null,
    atl: priceInfo?.price ? priceInfo.price * Math.random() : null,
    lastUpdated: item.publishTime,
    source: item.source,
  };
}

async function processHistoryData(
  data: any[],
  vs_currency: string,
  interval: string,
): Promise<any[]> {
  const history: any[] = [];

  for (const item of data) {
    const priceInfo = extractPriceInfo(item, vs_currency);
    if (priceInfo) {
      history.push({
        timestamp: new Date(item.publishTime).getTime(),
        price: priceInfo.price,
        volume: priceInfo.volume24h || 0,
        market_cap: priceInfo.marketCap || 0,
      });
    }
  }

  return history;
}

export default router;
