import { ConcurrentScraper } from './concurrent-scraper';
import { logger } from '../utils/logger';

/**
 * 爬虫管理器单例 - 管理全局唯一的ConcurrentScraper实例
 */
export class ScraperManager {
  private static instance: ScraperManager;
  private concurrentScraper: ConcurrentScraper | null = null;
  private isInitializing = false;
  private initializationPromise: Promise<void> | null = null;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): ScraperManager {
    if (!ScraperManager.instance) {
      ScraperManager.instance = new ScraperManager();
    }
    return ScraperManager.instance;
  }

  /**
   * 初始化并发抓取器（确保只初始化一次）
   */
  async initializeConcurrentScraper(mongoUrl?: string): Promise<ConcurrentScraper> {
    // 如果已经初始化完成，直接返回
    if (this.concurrentScraper) {
      return this.concurrentScraper;
    }

    // 如果正在初始化，等待初始化完成
    if (this.isInitializing && this.initializationPromise) {
      await this.initializationPromise;
      return this.concurrentScraper!;
    }

    // 开始初始化
    this.isInitializing = true;
    this.initializationPromise = this.performInitialization(mongoUrl);
    
    try {
      await this.initializationPromise;
      return this.concurrentScraper!;
    } finally {
      this.isInitializing = false;
      this.initializationPromise = null;
    }
  }

  /**
   * 执行实际的初始化
   */
  private async performInitialization(mongoUrl?: string): Promise<void> {
    try {
      const url = mongoUrl || process.env.MONGODB_URL || '****************************************************************************************';
      
      this.concurrentScraper = new ConcurrentScraper(url);
      await this.concurrentScraper.initializeConnections();
      
      logger.info('🎉 ScraperManager 单例初始化完成');
    } catch (error) {
      logger.error('❌ ScraperManager 初始化失败:', error);
      this.concurrentScraper = null;
      throw error;
    }
  }

  /**
   * 获取并发抓取器实例
   */
  getConcurrentScraper(): ConcurrentScraper | null {
    return this.concurrentScraper;
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.concurrentScraper !== null;
  }

  /**
   * 关闭所有连接并重置实例
   */
  async shutdown(): Promise<void> {
    if (this.concurrentScraper) {
      try {
        await this.concurrentScraper.closeAllConnections();
        logger.info('ScraperManager 连接已关闭');
      } catch (error) {
        logger.error('关闭ScraperManager连接失败:', error);
      } finally {
        this.concurrentScraper = null;
      }
    }
  }

  /**
   * 重新初始化（用于错误恢复）
   */
  async reinitialize(mongoUrl?: string): Promise<ConcurrentScraper> {
    await this.shutdown();
    return this.initializeConcurrentScraper(mongoUrl);
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    initialized: boolean;
    healthy: boolean;
    error?: string;
  }> {
    if (!this.concurrentScraper) {
      return {
        initialized: false,
        healthy: false,
        error: 'ConcurrentScraper 未初始化'
      };
    }

    try {
      const healthResult = await this.concurrentScraper.healthCheck();
      return {
        initialized: true,
        healthy: healthResult.overall !== 'unhealthy',
        error: healthResult.overall === 'unhealthy' ? '部分数据源不健康' : undefined
      };
    } catch (error) {
      return {
        initialized: true,
        healthy: false,
        error: (error as any)?.message || '健康检查失败'
      };
    }
  }
}

/**
 * 快捷函数：获取初始化的并发抓取器
 */
export async function getInitializedScraper(mongoUrl?: string): Promise<ConcurrentScraper> {
  const manager = ScraperManager.getInstance();
  return manager.initializeConcurrentScraper(mongoUrl);
}

/**
 * 快捷函数：获取抓取器实例（不自动初始化）
 */
export function getScraper(): ConcurrentScraper | null {
  const manager = ScraperManager.getInstance();
  return manager.getConcurrentScraper();
}