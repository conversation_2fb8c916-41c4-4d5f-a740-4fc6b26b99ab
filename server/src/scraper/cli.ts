#!/usr/bin/env node

import { Command } from 'commander';
import { MongoClient } from 'mongodb';
import { AccurateDataScraper } from './scraper';
import { ConcurrentScraper } from './concurrent-scraper';
import { logger } from '../utils/logger';

const program = new Command();

// MongoDB 连接配置
const MONGO_URL = process.env.MONGODB_URL || '****************************************************************************************';
const DB_NAME = process.env.MONGODB_DB_NAME || 'chainmix';

let client: MongoClient;
let scraper: AccurateDataScraper;
let concurrentScraper: ConcurrentScraper;

/**
 * 初始化数据库连接
 */
async function initializeDatabase(setName?: string): Promise<void> {
  const _NAME = setName || DB_NAME
  client = new MongoClient(MONGO_URL);
  await client.connect();
  const db = client.db(_NAME);
  scraper = new AccurateDataScraper(db);
  logger.info(`数据库连接成功: ${_NAME}`);
}

/**
 * 初始化并发抓取器
 */
async function initializeConcurrentScraper(): Promise<void> {
  concurrentScraper = new ConcurrentScraper(MONGO_URL);
  await concurrentScraper.initializeConnections();
  logger.info('并发抓取器初始化成功');
}

/**
 * 清理资源
 */
async function cleanup(): Promise<void> {
  if (concurrentScraper) {
    await concurrentScraper.closeAllConnections();
    logger.info('并发抓取器连接已关闭');
  }
  if (client) {
    await client.close();
    logger.info('数据库连接已关闭');
  }
}

program
  .name('accurate-scraper')
  .description('ChainMix 精确API数据抓取工具 - 基于实际API规范')
  .version('3.0.0');

// 初始化配置命令
program
  .command('init')
  .description('初始化精确的数据源配置和数据库索引')
  .action(async () => {
    try {
      await initializeDatabase();
      await scraper.initializeSourceConfigs();
      logger.info('初始化完成');
    } catch (error) {
      logger.error('初始化失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

program
  .command('flash')
  .description('flash 更新抓取')
  .action(async () => {
    //TODO: 更新抓取所有 endpoint 中 dataType 为 flash 的接口数据
  });

// 开始抓取命令
program
  .command('scrape <source> [dataType]')
  .description('开始抓取指定数据源')
  .option('-f, --force', '强制全量抓取')
  .option('-r, --resume', '恢复暂停的任务')
  .option('-m, --max-pages <pages>', '最大抓取页数', '0')
  .action(async (source: string, dataType?: string, options?: any) => {
    // 特殊处理 article 命令
    if (source === 'article' && dataType) {
      // 这是新的 article detail 抓取命令
      try {
        await initializeDatabase(`${dataType}_db`);
        logger.info(`开始抓取 ${dataType} 的文章详情`);
        await scraper.scrapeArticleDetails(dataType);
        logger.info(`文章详情抓取完成: ${dataType}`);
      } catch (error) {
        logger.error('文章详情抓取失败:', error);
        process.exit(1);
      } finally {
        await cleanup();
      }
      return;
    }
    try {
      // 完整的数据源和类型映射
      
      const sourceEndpoints = {
        'chaincatcher': ['article', 'flash'],
        'chainfeeds': ['feed', 'flash', 'topic', 'subject'],
        'followin': ['flash'],
        'odaily': ['depth', 'flash', 'post', 'price', 'tweet'],
        'techflow': ['flash'],
        'trendx': ['financing', 'news', 'tweet'],
        'theblockbeats': ['flash', 'finance'],
        'panewslab': ['flash'],
        'foresightnews': ['dayNews', 'news', 'feed', 'event', 'topic', 'column', 'article', 'articleDetail', 'tools', 'fundraising', 'calendars']
      };

      if (!source) { logger.error("source needed!"); process.exit(1) }
      if (!sourceEndpoints[source]) { logger.error("invalid source!"); process.exit(1) }
      await initializeDatabase(`${source}_db`);

      const maxPages = parseInt(options?.maxPages) || undefined;
      const scrapeOptions = {
        forceFullScrape: options?.force || false,
        resume: options?.resume || false,
        maxPages
      };


      if (dataType) {
        // 抓取特定数据类型
        logger.info(`开始抓取 ${source}-${dataType}`);
        await scraper.startScraping(source, dataType, scrapeOptions);
        logger.info(`抓取完成: ${source}-${dataType}`);
      } else {
        if (source === 'all') {
          // 抓取所有数据源
          for (const [src, types] of Object.entries(sourceEndpoints)) {
            for (const type of types) {
              try {
                logger.info(`开始抓取 ${src}-${type}`);
                await scraper.startScraping(src, type, scrapeOptions);
                logger.info(`抓取完成: ${src}-${type}`);
              } catch (error) {
                logger.error(`抓取失败 ${src}-${type}:`, error);
              }
            }
          }
        } else {
          // 抓取指定数据源的所有类型
          const types = sourceEndpoints[source as keyof typeof sourceEndpoints] || [];
          for (const type of types) {
            try {
              logger.info(`开始抓取 ${source}-${type}`);
              await scraper.startScraping(source, type, scrapeOptions);
              logger.info(`抓取完成: ${source}-${type}`);
            } catch (error) {
              logger.error(`抓取失败 ${source}-${type}:`, error);
            }
          }
        }
      }
    } catch (error) {
      logger.error('抓取失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 并发抓取所有数据源
program
  .command('scrape-concurrent')
  .description('使用并发方式抓取所有数据源 (推荐用于快速抓取)')
  .option('-f, --force', '强制全量抓取')
  .option('-m, --max-pages <pages>', '最大抓取页数', '0')
  .option('-c, --concurrent-sources <num>', '同时抓取的数据源数量', '10')
  .option('-p, --concurrent-per-source <num>', '每个数据源的并发端点数', '2')
  .option('-e, --exclude <sources>', '排除的数据源 (用逗号分隔)', '')
  .action(async (options: any) => {
    try {
      await initializeConcurrentScraper();

      const maxPages = parseInt(options?.maxPages) || undefined;
      const concurrentSources = parseInt(options?.concurrentSources) || 3;
      const concurrentPerSource = parseInt(options?.concurrentPerSource) || 2;
      const excludeSources = options?.exclude ? options.exclude.split(',').map((s: string) => s.trim()) : [];

      const scrapeOptions = {
        forceFullScrape: options?.force || false,
        maxPages,
        excludeSources,
        concurrentSources,
        concurrentPerSource
      };

      logger.info(`🚀 开始并发抓取所有数据源`);
      logger.info(`配置: 并发源=${concurrentSources}, 每源并发=${concurrentPerSource}, 排除=${excludeSources.join(',') || '无'}`);

      const result = await concurrentScraper.scrapeAllSources(scrapeOptions);

      // 输出详细结果
      console.log('\n=== 🎯 并发抓取完成 ===');
      console.log(`总耗时: ${result.summary.totalTime}`);
      console.log(`成功率: ${result.summary.successRate}`);
      console.log(`总端点: ${result.successCount}/${result.totalEndpoints}`);
      console.log(`平均每源耗时: ${result.summary.averageTimePerSource}`);
      console.log(`抓取速度: ${result.summary.endpointsPerMinute} 端点/分钟`);

      // 显示各数据源详情
      console.log('\n📊 各数据源详情:');
      for (const [source, sourceResult] of Object.entries(result.sourceResults)) {
        const status = sourceResult.error ? '❌' :
          sourceResult.successCount === sourceResult.totalEndpoints ? '✅' : '⚠️';
        console.log(`  ${status} ${source}: ${sourceResult.successCount}/${sourceResult.totalEndpoints} (${(sourceResult.duration / 1000).toFixed(1)}s)`);

        if (sourceResult.error) {
          console.log(`    错误: ${sourceResult.error}`);
        }
      }

      logger.info('并发抓取任务完成');
    } catch (error) {
      logger.error('并发抓取失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 列出所有可用的API端点
program
  .command('list-endpoints')
  .description('列出所有可用的API端点')
  .action(async () => {
    console.log('\n=== 可用的API端点 ===\n');

    const endpoints = {
      'chaincatcher': {
        'article': '文章 - ChainCatcher文章内容',
        'flash': '快讯 - ChainCatcher快讯消息'
      },
      'chainfeeds': {
        'feed': '精选资讯 - ChainFeeds精选内容',
        'flash': '快讯 - ChainFeeds快讯',
        'topic': '主题新闻 - ChainFeeds主题聚合',
        'subject': '原创文章 - ChainFeeds原创内容'
      },
      'followin': {
        'flash': '快讯 - Followin推荐新闻'
      },
      'odaily': {
        'depth': '深度文章 - Odaily深度分析',
        'flash': '快讯 - Odaily快讯消息',
        'post': '文章 - Odaily普通文章',
        'price': '价格 - 主要加密货币价格',
        'tweet': '观点 - 用户观点和推文'
      },
      'techflow': {
        'flash': '快讯 - TechFlow快讯消息'
      },
      'trendx': {
        'financing': '融资信息 - 项目融资数据',
        'news': '新闻 - TrendX新闻聚合',
        'tweet': '推文 - Twitter相关内容'
      },
      'theblockbeats': {
        'flash': '快讯 - 律动BlockBeats快讯',
        'finance': '融资 - 律动BlockBeats融资信息'
      },
      'panewslab': {
        'flash': '快讯 - PANews快讯消息'
      },
      'foresightnews': {
        'dayNews': '日报 - 每日快讯汇总',
        'news': '新闻 - ForesightNews新闻',
        'feed': '订阅源 - ForesightNews订阅内容',
        'event': '事件 - 区块链事件',
        'topic': '专题 - 热门专题',
        'column': '专栏 - 专栏文章',
        'article': '文章 - 专栏具体文章',
        'articleDetail': '文章详情 - 专栏文章的详细内容',
        'tools': '工具 - 区块链工具集',
        'fundraising': '融资 - 融资信息',
        'calendars': '日历 - 事件日历'
      }
    };

    for (const [source, types] of Object.entries(endpoints)) {
      console.log(`📰 ${source.toUpperCase()}`);
      for (const [type, description] of Object.entries(types)) {
        console.log(`   ${type}: ${description}`);
      }
      console.log('');
    }

    console.log('使用方法:');
    console.log('  node dist/scraper/accurate-cli.js scrape <source> [dataType]');
    console.log('  node dist/scraper/accurate-cli.js scrape article <source>  # 抓取指定源的文章详情');
    console.log('  例如: node dist/scraper/accurate-cli.js scrape chaincatcher article');
    console.log('       node dist/scraper/accurate-cli.js scrape foresightnews');
    console.log('       node dist/scraper/accurate-cli.js scrape article foresightnews  # 抓取foresightnews文章详情');
    console.log('       node dist/scraper/accurate-cli.js scrape all');
  });

// 查看任务状态命令
program
  .command('status [source] [dataType]')
  .description('查看抓取任务状态')
  .action(async (source?: string, dataType?: string) => {
    try {
      await initializeDatabase();

      if (source && dataType) {
        // 查看特定任务状态
        const task = await scraper.getTaskStatus(source, dataType);
        if (task) {
          console.log('\n=== 任务状态 ===');
          console.log(`数据源: ${task.source}`);
          console.log(`数据类型: ${task.dataType}`);
          console.log(`状态: ${task.status}`);
          console.log(`进度: ${task.processedItems}/${task.totalItems || '未知'}`);
          console.log(`当前页: ${task.lastPageProcessed || 1}`);
          console.log(`失败项: ${task.failedItems}`);
          console.log(`开始时间: ${task.startTime?.toLocaleString() || '未开始'}`);
          console.log(`更新时间: ${task.updatedAt.toLocaleString()}`);
          if (task.errorMessage) {
            console.log(`错误信息: ${task.errorMessage}`);
          }
        } else {
          console.log('未找到任务');
        }
      } else {
        // 查看所有任务状态
        const tasks = await scraper.getAllTasks();
        console.log('\n=== 所有任务状态 ===');

        if (tasks.length === 0) {
          console.log('暂无任务');
          return;
        }

        tasks.forEach((task, index) => {
          const status = task.status === 'completed' ? '✅' :
            task.status === 'running' ? '🔄' :
              task.status === 'failed' ? '❌' :
                task.status === 'paused' ? '⏸️' : '⏳';

          console.log(`\n${index + 1}. ${status} ${task.source}-${task.dataType}`);
          console.log(`   状态: ${task.status}`);
          console.log(`   进度: ${task.processedItems}/${task.totalItems || '未知'}`);
          console.log(`   更新: ${task.updatedAt.toLocaleString()}`);
          if (task.errorMessage) {
            console.log(`   错误: ${task.errorMessage}`);
          }
        });
      }
    } catch (error) {
      logger.error('获取状态失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 统计信息命令
program
  .command('stats')
  .description('显示详细的抓取统计信息')
  .action(async () => {
    try {
      await initializeDatabase();
      const stats = await scraper.getStats();

      console.log('\n=== 📊 抓取统计信息 ===');
      console.log(`总数据项数: ${stats.totalItems}`);
      console.log(`总任务数: ${stats.totalTasks}`);

      if (stats.itemsBySource.length > 0) {
        console.log('\n📈 各数据源统计:');
        stats.itemsBySource
          .sort((a: any, b: any) => b.count - a.count)
          .forEach((item: any) => {
            const latestTime = item.latestItem ? new Date(item.latestItem).toLocaleString() : '无数据';
            console.log(`   ${item._id.source}-${item._id.dataType}: ${item.count} 项 (最新: ${latestTime})`);
          });
      }

      if (stats.tasksByStatus.length > 0) {
        console.log('\n📋 任务状态统计:');
        stats.tasksByStatus.forEach((item: any) => {
          const statusIcon = item._id === 'completed' ? '✅' :
            item._id === 'running' ? '🔄' :
              item._id === 'failed' ? '❌' :
                item._id === 'paused' ? '⏸️' : '⏳';
          console.log(`   ${statusIcon} ${item._id}: ${item.count} 个任务`);
        });
      }

      console.log(`\n🕐 统计生成时间: ${stats.generatedAt.toLocaleString()}`);
    } catch (error) {
      logger.error('获取统计信息失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 跨数据源统计信息命令
program
  .command('stats-all')
  .description('显示所有数据源的详细统计信息')
  .action(async () => {
    try {
      await initializeConcurrentScraper();

      console.log('\n=== 📊 跨数据源统计信息 ===');

      const allStats = await concurrentScraper.getAllSourcesStats();

      console.log(`总数据项数: ${allStats.totalItems}`);
      console.log(`总任务数: ${allStats.totalTasks}`);
      console.log(`数据源数量: ${Object.keys(allStats.sources).length}`);

      console.log('\n🏢 各数据源详情:');
      for (const [source, stats] of Object.entries(allStats.sources)) {
        console.log(`\n📰 ${source.toUpperCase()}`);
        console.log(`   数据库: ${stats.database}`);
        console.log(`   数据项: ${stats.totalItems}`);
        console.log(`   任务数: ${stats.totalTasks}`);

        if (stats.error) {
          console.log(`   状态: ❌ 错误 - ${stats.error}`);
        } else {
          console.log(`   状态: ✅ 正常`);

          if (stats.itemsBySource && stats.itemsBySource.length > 0) {
            console.log('   数据类型分布:');
            stats.itemsBySource
              .sort((a: any, b: any) => b.count - a.count)
              .forEach((item: any) => {
                const latestTime = item.latestItem ? new Date(item.latestItem).toLocaleString() : '无数据';
                console.log(`     ${item._id.dataType}: ${item.count} 项 (最新: ${latestTime})`);
              });
          }
        }
      }

      console.log(`\n🕐 统计生成时间: ${allStats.generatedAt.toLocaleString()}`);
    } catch (error) {
      logger.error('获取跨数据源统计信息失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 跨数据源搜索
program
  .command('search-all')
  .description('在所有数据源中搜索内容')
  .option('-k, --keyword <keyword>', '搜索关键词 (必需)')
  .option('-s, --sources <sources>', '指定数据源 (用逗号分隔)', '')
  .option('-t, --types <types>', '指定数据类型 (用逗号分隔)', '')
  .option('-l, --limit <limit>', '结果限制数量', '50')
  .action(async (options: any) => {
    try {
      if (!options.keyword) {
        console.log('❌ 请提供搜索关键词，使用 -k 或 --keyword 参数');
        return;
      }

      await initializeConcurrentScraper();

      const searchQuery = {
        keyword: options.keyword,
        sources: options.sources ? options.sources.split(',').map((s: string) => s.trim()) : undefined,
        dataTypes: options.types ? options.types.split(',').map((t: string) => t.trim()) : undefined,
        limit: parseInt(options.limit) || 50
      };

      console.log(`\n🔍 在所有数据源中搜索: "${options.keyword}"`);

      const results = await concurrentScraper.searchAcrossAllSources(searchQuery);

      console.log(`\n=== 搜索结果 (${results.totalResults} 条，耗时 ${results.searchTime}ms) ===`);

      // 显示各数据源的结果数量
      console.log('\n📊 各数据源结果:');
      for (const [source, sourceResult] of Object.entries(results.sources)) {
        const status = sourceResult.error ? '❌' : '✅';
        console.log(`  ${status} ${source}: ${sourceResult.count} 条${sourceResult.error ? ` (${sourceResult.error})` : ''}`);
      }

      // 显示聚合结果
      if (results.aggregatedResults.length > 0) {
        console.log('\n📄 搜索结果详情:');
        results.aggregatedResults.slice(0, 20).forEach((item: any, index: number) => {
          console.log(`\n${index + 1}. 📰 ${item.title}`);
          console.log(`   来源: ${item.source}-${item.dataType}`);
          console.log(`   时间: ${new Date(item.publishTime).toLocaleString()}`);
          if (item.content) {
            console.log(`   内容: ${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}`);
          }
          if (item.url) {
            console.log(`   链接: ${item.url}`);
          }
        });

        if (results.aggregatedResults.length > 20) {
          console.log(`\n... 还有 ${results.aggregatedResults.length - 20} 条结果`);
        }
      } else {
        console.log('\n❌ 未找到匹配的结果');
      }
    } catch (error) {
      logger.error('跨数据源搜索失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 数据查询命令
program
  .command('query')
  .description('查询抓取的数据')
  .option('-s, --source <source>', '按数据源过滤')
  .option('-t, --type <type>', '按数据类型过滤')
  .option('-l, --limit <limit>', '限制数量', '10')
  .option('-k, --keyword <keyword>', '关键词搜索')
  .action(async (options: any) => {
    try {
      await initializeDatabase();

      const db = (scraper as any).db;
      const collection = db.collection('scraped_items');

      // 构建查询条件
      const filter: any = {};
      if (options.source) filter.source = options.source;
      if (options.type) filter.dataType = options.type;
      if (options.keyword) {
        filter.$or = [
          { title: { $regex: options.keyword, $options: 'i' } },
          { content: { $regex: options.keyword, $options: 'i' } }
        ];
      }

      const items = await collection
        .find(filter)
        .sort({ publishTime: -1 })
        .limit(parseInt(options.limit))
        .toArray();

      console.log(`\n=== 🔍 查询结果 (${items.length} 条) ===\n`);

      items.forEach((item: any, index: number) => {
        console.log(`${index + 1}. 📰 ${item.title}`);
        console.log(`   来源: ${item.source}-${item.dataType}`);
        console.log(`   时间: ${new Date(item.publishTime).toLocaleString()}`);
        if (item.content) {
          console.log(`   内容: ${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}`);
        }
        if (item.url) {
          console.log(`   链接: ${item.url}`);
        }
        if (item.tags && item.tags.length > 0) {
          console.log(`   标签: ${item.tags.join(', ')}`);
        }
        console.log('');
      });
    } catch (error) {
      logger.error('查询数据失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 清理数据命令
program
  .command('cleanup')
  .description('清理过期数据')
  .option('-d, --days <days>', '保留天数', '30')
  .action(async (options: any) => {
    try {
      await initializeDatabase();
      const days = parseInt(options.days);
      await scraper.cleanupOldData(days);
      logger.info(`清理了 ${days} 天前的数据`);
    } catch (error) {
      logger.error('清理数据失败:', error);
      process.exit(1);
    } finally {
      await cleanup();
    }
  });

// 交互模式命令
program
  .command('interactive')
  .description('进入交互模式')
  .action(async () => {
    try {
      await initializeDatabase();

      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      console.log('\n=== 🤖 ChainMix 精确数据抓取交互模式 ===');
      console.log('基于实际API规范的高精度数据抓取系统');
      console.log('\n可用命令:');
      console.log('  📝 init - 初始化配置');
      console.log('  🚀 scrape <source> [dataType] [--force] - 开始抓取');
      console.log('  📊 status [source] [dataType] - 查看任务状态');
      console.log('  📈 stats - 显示统计信息');
      console.log('  🔍 query [--source] [--type] [--keyword] - 查询数据');
      console.log('  📋 list - 列出所有API端点');
      console.log('  🧹 cleanup [--days N] - 清理过期数据');
      console.log('  ❓ help - 显示帮助');
      console.log('  🚪 exit - 退出');

      const askCommand = () => {
        rl.question('\n💬 请输入命令: ', async (input: string) => {
          const parts = input.trim().split(' ');
          const command = parts[0];

          try {
            switch (command) {
              case 'init':
                await scraper.initializeSourceConfigs();
                console.log('✅ 配置初始化完成');
                break;

              case 'scrape':
                if (parts.length < 2) {
                  console.log('❌ 请指定数据源，用法: scrape <source> [dataType] [--force]');
                } else {
                  const source = parts[1];
                  const dataType = parts[2] !== '--force' ? parts[2] : undefined;
                  const forceIndex = parts.indexOf('--force');

                  const options = {
                    forceFullScrape: forceIndex !== -1
                  };

                  console.log(`🚀 开始抓取 ${source}${dataType ? `-${dataType}` : ''} ${options.forceFullScrape ? '(强制全量)' : ''}`);

                  if (dataType) {
                    await scraper.startScraping(source, dataType, options);
                  } else {
                    const sourceEndpoints = {
                      'chaincatcher': ['article', 'flash'],
                      'chainfeeds': ['feed', 'flash', 'topic', 'subject'],
                      'followin': ['flash'],
                      'odaily': ['depth', 'flash', 'post', 'price', 'tweet'],
                      'techflow': ['flash'],
                      'trendx': ['financing', 'news', 'tweet'],
                      'theblockbeats': ['flash', 'finance'],
                      'panewslab': ['flash'],
                      'foresightnews': ['dayNews', 'news', 'feed', 'event', 'topic', 'column', 'article', 'articleDetail', 'tools', 'fundraising', 'calendars']
                    };

                    if (source === 'all') {
                      for (const [src, types] of Object.entries(sourceEndpoints)) {
                        for (const type of types) {
                          console.log(`📰 抓取 ${src}-${type}...`);
                          await scraper.startScraping(src, type, options);
                        }
                      }
                    } else {
                      const types = sourceEndpoints[source as keyof typeof sourceEndpoints] || [];
                      for (const type of types) {
                        console.log(`📰 抓取 ${source}-${type}...`);
                        await scraper.startScraping(source, type, options);
                      }
                    }
                  }
                  console.log('✅ 抓取完成');
                }
                break;

              case 'status':
                if (parts.length >= 3) {
                  const task = await scraper.getTaskStatus(parts[1], parts[2]);
                  if (task) {
                    const status = task.status === 'completed' ? '✅' :
                      task.status === 'running' ? '🔄' :
                        task.status === 'failed' ? '❌' :
                          task.status === 'paused' ? '⏸️' : '⏳';
                    console.log(`${status} ${task.source}-${task.dataType}: ${task.status} (${task.processedItems}项)`);
                  } else {
                    console.log('❌ 未找到任务');
                  }
                } else {
                  const tasks = await scraper.getAllTasks();
                  console.log('📋 任务列表:');
                  tasks.slice(0, 10).forEach(task => {
                    const status = task.status === 'completed' ? '✅' :
                      task.status === 'running' ? '🔄' :
                        task.status === 'failed' ? '❌' :
                          task.status === 'paused' ? '⏸️' : '⏳';
                    console.log(`   ${status} ${task.source}-${task.dataType}: ${task.status} (${task.processedItems}项)`);
                  });
                }
                break;

              case 'stats':
                const stats = await scraper.getStats();
                console.log(`📊 总计: ${stats.totalItems} 项数据, ${stats.totalTasks} 个任务`);
                if (stats.itemsBySource.length > 0) {
                  console.log('📈 数据分布:');
                  stats.itemsBySource.slice(0, 5).forEach((item: any) => {
                    console.log(`   ${item._id.source}-${item._id.dataType}: ${item.count} 项`);
                  });
                }
                break;

              case 'query':
                const db = (scraper as any).db;
                const collection = db.collection('scraped_items');
                const recentItems = await collection.find({}).sort({ publishTime: -1 }).limit(5).toArray();
                console.log('🔍 最新5条数据:');
                recentItems.forEach((item: any, index: number) => {
                  console.log(`   ${index + 1}. ${item.title} (${item.source}-${item.dataType})`);
                });
                break;

              case 'list':
                console.log('📋 主要API端点:');
                console.log('   chaincatcher: article, flash');
                console.log('   chainfeeds: feed, flash, topic, subject');
                console.log('   followin: flash');
                console.log('   odaily: depth, flash, post, price, tweet');
                console.log('   techflow: flash');
                console.log('   trendx: financing, news, tweet');
                console.log('   theblockbeats: flash, finance');
                console.log('   panewslab: flash');
                console.log('   foresightnews: dayNews, news, feed, event, topic, column, article, articleDetail, tools, fundraising, calendars');
                break;

              case 'cleanup':
                const daysIndex = parts.indexOf('--days');
                const days = daysIndex !== -1 && parts[daysIndex + 1] ? parseInt(parts[daysIndex + 1]) : 30;
                await scraper.cleanupOldData(days);
                console.log(`🧹 清理了 ${days} 天前的数据`);
                break;

              case 'help':
                console.log('📖 可用命令:');
                console.log('   init - 初始化配置');
                console.log('   scrape <source> [dataType] [--force] - 开始抓取');
                console.log('   status [source] [dataType] - 查看任务状态');
                console.log('   stats - 显示统计信息');
                console.log('   query - 查询最新数据');
                console.log('   list - 列出API端点');
                console.log('   cleanup [--days N] - 清理过期数据');
                console.log('   exit - 退出');
                break;

              case 'exit':
                console.log('👋 退出交互模式');
                rl.close();
                return;

              default:
                if (command) {
                  console.log('❌ 未知命令，输入 help 查看帮助');
                }
            }
          } catch (error) {
            console.error('❌ 命令执行失败:', (error as any)?.message || '未知错误');
          }

          askCommand();
        });
      };

      askCommand();
    } catch (error) {
      logger.error('交互模式失败:', error);
      process.exit(1);
    }
  });

program
  .command('deleteTasks')
  .description('删除所有pending人物')
  .action(async () => {
    try {
      await initializeConcurrentScraper()
      await concurrentScraper.deleteTasksCollection()
      process.exit(0);
    } catch (error) {
      logger.error('deleteTasks:', error);
      process.exit(1);
    }
  });

program
  .command('dump')
  .description('导出所有数据到JSON文件')
  .action(async () => {
    try {
      await initializeConcurrentScraper()
      await concurrentScraper.dump()
      process.exit(0);
    } catch (error) {
      logger.error('dump:', error);
      process.exit(1);
    }
  });

// 错误处理
process.on('uncaughtException', async (error) => {
  logger.error('未捕获的异常:', error);
  await cleanup();
  process.exit(1);
});

process.on('unhandledRejection', async (reason) => {
  logger.error('未处理的Promise拒绝:', reason);
  await cleanup();
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', async () => {
  logger.info('收到中断信号，正在清理资源...');
  await cleanup();
  process.exit(0);
});

program.parse();