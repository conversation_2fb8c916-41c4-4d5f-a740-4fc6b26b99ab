import { MongoClient, Db } from 'mongodb';
import { logger } from '../utils/logger';
import { AccurateDataScraper } from './scraper';

/**
 * 并发抓取管理器 - 支持异步并发抓取和独立数据库存储
 */
export class ConcurrentScraper {
  private clients: Map<string, MongoClient> = new Map();
  private scrapers: Map<string, AccurateDataScraper> = new Map();
  private databases: Map<string, Db> = new Map();

  // 并发控制
  private maxConcurrentSources = 10; // 最多同时抓取10个数据源
  private maxConcurrentPerSource = 10; // 每个数据源最多并发10个数据类型
  private activeTasks: Map<string, Promise<void>> = new Map();

  // 数据源到数据库名的映射
  private readonly sourceDbMapping = {
    chaincatcher: 'chaincatcher_db',
    chainfeeds: 'chainfeeds_db',
    followin: 'followin_db',
    odaily: 'odaily_db',
    techflow: 'techflow_db',
    trendx: 'trendx_db',
    theblockbeats: 'theblockbeats_db',
    panewslab: 'panewslab_db',
    foresightnews: 'foresightnews_db',
  };

  constructor(private baseMongoUrl: string) {}

  /**
   * 初始化所有数据源的独立数据库连接
   */
  async initializeConnections(): Promise<void> {
    logger.info('开始初始化所有数据源的独立数据库连接...');

    const initPromises = Object.entries(this.sourceDbMapping).map(async ([source, dbName]) => {
      try {
        // 为每个数据源创建独立连接和数据库
        const client = new MongoClient(this.baseMongoUrl);
        await client.connect();

        const db = client.db(dbName);
        const scraper = new AccurateDataScraper(db);

        // 存储连接、数据库和抓取器实例
        this.clients.set(source, client);
        this.databases.set(source, db);
        this.scrapers.set(source, scraper);

        // 为每个数据源初始化配置
        await scraper.initializeSourceConfigs();

        logger.info(`✅ ${source} 数据库连接已建立: ${dbName}`);
      } catch (error) {
        logger.error(`❌ ${source} 数据库连接失败:`, error);
        throw error;
      }
    });

    await Promise.all(initPromises);
    logger.info(
      `🎉 所有 ${Object.keys(this.sourceDbMapping).length} 个数据源的数据库连接已建立完成`,
    );
  }

  /**
   * 关闭所有数据库连接
   */
  async closeAllConnections(): Promise<void> {
    const closePromises = Array.from(this.clients.values()).map(client => client.close());
    await Promise.all(closePromises);

    this.clients.clear();
    this.scrapers.clear();
    this.databases.clear();

    logger.info('所有数据库连接已关闭');
  }

  /**
   * 异步并发抓取所有数据源
   */
  async scrapeAllSources(
    options: {
      forceFullScrape?: boolean;
      maxPages?: number;
      excludeSources?: string[];
      concurrentSources?: number;
      concurrentPerSource?: number;
    } = {},
  ): Promise<ConcurrentScrapeResult> {
    const {
      forceFullScrape = false,
      maxPages,
      excludeSources = [],
      concurrentSources = this.maxConcurrentSources,
      concurrentPerSource = this.maxConcurrentPerSource,
    } = options;

    logger.info(
      `🚀 开始并发抓取所有数据源 (并发源: ${concurrentSources}, 每源并发: ${concurrentPerSource})`,
    );

    const startTime = Date.now();
    const results: ConcurrentScrapeResult = {
      startTime: new Date(),
      endTime: new Date(),
      duration: 0,
      totalSources: 0,
      totalEndpoints: 0,
      successCount: 0,
      failedCount: 0,
      sourceResults: {},
      summary: {
        totalTime: '0s',
        averageTimePerSource: '0s',
        successRate: '0%',
        sourcesCompleted: 0,
        endpointsPerMinute: 0,
      },
    };

    // 获取所有需要抓取的数据源
    const sourcesToScrape = Object.keys(this.sourceDbMapping).filter(
      source => !excludeSources.includes(source),
    );

    results.totalSources = sourcesToScrape.length;

    // 使用信号量控制并发数量
    const sourceSemaphore = new Semaphore(concurrentSources);

    // 为每个数据源创建抓取任务
    const sourcePromises = sourcesToScrape.map(async source => {
      return sourceSemaphore.acquire(async () => {
        try {
          logger.info(`📡 开始抓取数据源: ${source}`);

          const sourceResult = await this.scrapeSourceConcurrently(source, {
            forceFullScrape,
            maxPages,
            maxConcurrent: concurrentPerSource,
          });

          results.sourceResults[source] = sourceResult;
          results.totalEndpoints += sourceResult.totalEndpoints;
          results.successCount += sourceResult.successCount;
          results.failedCount += sourceResult.failedCount;

          logger.info(
            `✅ 数据源 ${source} 抓取完成: 成功 ${sourceResult.successCount}/${sourceResult.totalEndpoints}`,
          );
        } catch (error) {
          logger.error(`❌ 数据源 ${source} 抓取失败:`, error);
          results.sourceResults[source] = {
            source,
            startTime: new Date(),
            endTime: new Date(),
            duration: 0,
            totalEndpoints: 0,
            successCount: 0,
            failedCount: 0,
            endpoints: {},
            error: (error as any)?.message || '未知错误',
          };
        }
      });
    });

    // 等待所有数据源抓取完成
    await Promise.all(sourcePromises);

    // 计算总体统计
    results.endTime = new Date();
    results.duration = Date.now() - startTime;

    // 生成摘要信息
    results.summary = {
      totalTime: `${(results.duration / 1000).toFixed(2)}s`,
      averageTimePerSource: `${(results.duration / results.totalSources / 1000).toFixed(2)}s`,
      successRate: `${((results.successCount / Math.max(results.totalEndpoints, 1)) * 100).toFixed(1)}%`,
      sourcesCompleted: Object.keys(results.sourceResults).length,
      endpointsPerMinute: Math.round(results.totalEndpoints / (results.duration / 60000) || 0),
    };

    logger.info(
      `🎯 并发抓取完成! 总计: ${results.successCount}/${results.totalEndpoints} 成功, 耗时: ${results.summary.totalTime}`,
    );

    return results;
  }

  /**
   * 并发抓取单个数据源的所有端点
   */
  private async scrapeSourceConcurrently(
    source: string,
    options: {
      forceFullScrape?: boolean;
      maxPages?: number;
      maxConcurrent?: number;
    },
  ): Promise<SourceScrapeResult> {
    const scraper = this.scrapers.get(source);
    if (!scraper) {
      throw new Error(`数据源 ${source} 未初始化`);
    }

    const startTime = Date.now();
    const result: SourceScrapeResult = {
      source,
      startTime: new Date(),
      endTime: new Date(),
      duration: 0,
      totalEndpoints: 0,
      successCount: 0,
      failedCount: 0,
      endpoints: {},
    };

    // 获取该数据源的所有端点
    const endpoints = this.getSourceEndpoints(source);
    result.totalEndpoints = endpoints.length;

    // 使用信号量控制该数据源内的并发数
    const endpointSemaphore = new Semaphore(options.maxConcurrent || 2);

    // 为每个端点创建并发抓取任务
    const endpointPromises = endpoints.map(async dataType => {
      return endpointSemaphore.acquire(async () => {
        try {
          logger.debug(`  🔄 ${source}-${dataType} 抓取开始`);

          const taskStartTime = Date.now();
          const task = await scraper.startScraping(source, dataType, {
            forceFullScrape: options.forceFullScrape,
            maxPages: options.maxPages,
          });

          const taskDuration = Date.now() - taskStartTime;

          result.endpoints[dataType] = {
            dataType,
            success: true,
            duration: taskDuration,
            taskId: task._id,
            processedItems: task.processedItems || 0,
          };

          result.successCount++;
          logger.debug(
            `  ✅ ${source}-${dataType} 抓取成功 (${taskDuration}ms, ${task.processedItems || 0} 项)`,
          );
        } catch (error) {
          result.endpoints[dataType] = {
            dataType,
            success: false,
            duration: 0,
            error: (error as any)?.message || '未知错误',
            processedItems: 0,
          };

          result.failedCount++;
          logger.debug(`  ❌ ${source}-${dataType} 抓取失败: ${(error as any)?.message}`);
        }
      });
    });

    // 等待所有端点抓取完成
    await Promise.all(endpointPromises);

    result.endTime = new Date();
    result.duration = Date.now() - startTime;

    return result;
  }

  /**
   * 获取指定数据源的所有端点
   */
  private getSourceEndpoints(source: string): string[] {
    const endpointMapping = {
      chaincatcher: ['article', 'flash'],
      chainfeeds: ['feed', 'flash', 'topic', 'subject'],
      followin: ['flash'],
      odaily: ['depth', 'flash', 'post', 'price', 'tweet'],
      techflow: ['flash'],
      trendx: ['financing', 'news', 'tweet'],
      theblockbeats: ['flash', 'finance'],
      panewslab: ['flash'],
      foresightnews: [
        'dayNews',
        'news',
        'feed',
        'event',
        'topic',
        'column',
        'article',
        'tools',
        'fundraising',
        'calendars',
      ],
    };

    return endpointMapping[source as keyof typeof endpointMapping] || [];
  }

  /**
   * 获取所有数据源的统计信息
   */
  async getAllSourcesStats(): Promise<AllSourcesStats> {
    const stats: AllSourcesStats = {
      sources: {},
      totalItems: 0,
      totalTasks: 0,
      generatedAt: new Date(),
    };

    const statsPromises = Array.from(this.scrapers.entries()).map(async ([source, scraper]) => {
      try {
        const sourceStats = await scraper.getStats();
        stats.sources[source] = {
          ...sourceStats,
          database: this.sourceDbMapping[source as keyof typeof this.sourceDbMapping],
        };
        stats.totalItems += sourceStats.totalItems;
        stats.totalTasks += sourceStats.totalTasks;
      } catch (error) {
        logger.error(`获取 ${source} 统计信息失败:`, error);
        stats.sources[source] = {
          totalItems: 0,
          totalTasks: 0,
          itemsBySource: [],
          tasksByStatus: [],
          generatedAt: new Date(),
          database: this.sourceDbMapping[source as keyof typeof this.sourceDbMapping],
          error: (error as any)?.message,
        };
      }
    });

    await Promise.all(statsPromises);
    return stats;
  }

  /**
   * 从所有数据源搜索数据
   */
  async searchAcrossAllSources(query: {
    keyword: string;
    sources?: string[];
    dataTypes?: string[];
    limit?: number;
    sortBy?: string;
  }): Promise<CrossSourceSearchResult> {
    let {
      keyword,
      sources = Object.keys(this.sourceDbMapping),
      dataTypes = [],
      limit = 100,
      sortBy = 'publishTime',
    } = query;
    if (limit < 100) limit = 100;
    const results: CrossSourceSearchResult = {
      keyword,
      totalResults: 0,
      sources: {},
      aggregatedResults: [],
      searchTime: 0,
    };

    const startTime = Date.now();

    // 并发搜索所有指定数据源
    const searchPromises = sources.map(async source => {
      const scraper = this.scrapers.get(source);
      if (!scraper) {
        results.sources[source] = { items: [], count: 0, error: '数据源未初始化' };
        return;
      }

      try {
        const db = this.databases.get(source)!;
        const collection = db.collection('scraped_items');

        // 构建搜索条件
        const filter: any = {
          $or: [
            { title: { $regex: keyword, $options: 'i' } },
            { content: { $regex: keyword, $options: 'i' } },
          ],
        };

        if (dataTypes.length > 0) {
          filter.dataType = { $in: dataTypes };
        }

        const items = await collection
          .find(filter)
          .sort({ [sortBy]: -1 })
          .limit(Math.ceil(limit / sources.length)) // 每个源平均分配限额
          .toArray();

        results.sources[source] = {
          items,
          count: items.length,
        };

        results.totalResults += items.length;
        results.aggregatedResults.push(...items);
      } catch (error) {
        logger.error(`搜索 ${source} 失败:`, error);
        results.sources[source] = {
          items: [],
          count: 0,
          error: (error as any)?.message,
        };
      }
    });

    await Promise.all(searchPromises);

    // 对聚合结果进行排序和限制
    results.aggregatedResults = results.aggregatedResults
      .sort((a, b) => {
        if (sortBy === 'publishTime') {
          return new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime();
        }
        return 0;
      })
      .slice(0, limit);

    results.searchTime = Date.now() - startTime;
    return results;
  }

  /**
   * 专门抓取Flash数据的并发方法
   */
  async scrapeFlashDataConcurrently(
    targetSources: string[],
    options: {
      forceFullScrape?: boolean;
      maxPages?: number;
      concurrentSources?: number;
      flashOnly?: boolean;
    } = {}
  ): Promise<FlashScrapeResult> {
    const { forceFullScrape = false, maxPages, concurrentSources = 5 } = options;

    const startTime = Date.now();
    const result: FlashScrapeResult = {
      sourceResults: {},
      successCount: 0,
      failedCount: 0,
      totalSources: targetSources.length,
      summary: {
        totalTime: '0s',
        successRate: '0%',
        averageTimePerSource: '0s',
      },
    };

    // 使用信号量控制并发数
    const sourceSemaphore = new Semaphore(concurrentSources);

    // 为每个数据源创建Flash抓取任务
    const sourcePromises = targetSources.map(async source => {
      return sourceSemaphore.acquire(async () => {
        const sourceStartTime = Date.now();

        try {
          logger.info(`📡 开始抓取 ${source} 的flash数据`);

          const scraper = this.scrapers.get(source);
          if (!scraper) {
            throw new Error(`数据源 ${source} 未初始化`);
          }

          // 只抓取flash数据类型
          const task = await scraper.startScraping(source, 'flash', {
            forceFullScrape,
            maxPages,
          });

          const duration = Date.now() - sourceStartTime;

          result.sourceResults[source] = {
            source,
            success: true,
            duration,
            processedItems: task.processedItems || 0,
          };

          result.successCount++;
          logger.info(`✅ ${source} flash数据抓取成功 (${duration}ms, ${task.processedItems || 0} 项)`);
        } catch (error) {
          const duration = Date.now() - sourceStartTime;

          result.sourceResults[source] = {
            source,
            success: false,
            duration,
            error: (error as any)?.message || '未知错误',
          };

          result.failedCount++;
          logger.error(`❌ ${source} flash数据抓取失败:`, error);
        }
      });
    });

    // 等待所有任务完成
    await Promise.all(sourcePromises);

    // 计算汇总信息
    const totalTime = Date.now() - startTime;
    result.summary.totalTime = `${(totalTime / 1000).toFixed(1)}s`;
    result.summary.successRate = `${((result.successCount / result.totalSources) * 100).toFixed(1)}%`;
    result.summary.averageTimePerSource = `${(totalTime / result.totalSources / 1000).toFixed(1)}s`;

    return result;
  }

  /**
   * 获取特定数据源的抓取器实例
   */
  getSourceScraper(source: string): AccurateDataScraper | undefined {
    return this.scrapers.get(source);
  }

  /**
   * 获取特定数据源的数据库实例
   */
  getSourceDatabase(source: string): Db | undefined {
    return this.databases.get(source);
  }

  /**
   * 检查所有连接的健康状态
   */
  async healthCheck(): Promise<HealthCheckResult> {
    const result: HealthCheckResult = {
      overall: 'healthy',
      sources: {},
      timestamp: new Date(),
    };

    const healthPromises = Array.from(this.clients.entries()).map(async ([source, client]) => {
      try {
        // 简单的ping检查
        await client.db('admin').admin().ping();
        result.sources[source] = {
          status: 'healthy',
          database: this.sourceDbMapping[source as keyof typeof this.sourceDbMapping],
        };
      } catch (error) {
        result.sources[source] = {
          status: 'unhealthy',
          database: this.sourceDbMapping[source as keyof typeof this.sourceDbMapping],
          error: (error as any)?.message,
        };
        result.overall = 'degraded';
      }
    });

    await Promise.all(healthPromises);

    // 如果有任何源不健康，整体状态为降级
    const unhealthySources = Object.values(result.sources).filter(s => s.status === 'unhealthy');
    if (unhealthySources.length > 0) {
      result.overall =
        unhealthySources.length === Object.keys(result.sources).length ? 'unhealthy' : 'degraded';
    }

    return result;
  }

  /**
   * 获取数据源映射配置
   */
  getSourceDatabaseMapping(): Record<string, string> {
    return { ...this.sourceDbMapping };
  }
  /**
   * 删除 tasksCollection
   */
  async deleteTasksCollection(): Promise<void> {
    for (let [, scraper] of this.scrapers) {
      await scraper.deleteTasksCollection();
    }
  }
  async dump(): Promise<void> {
    for (let [name, scraper] of this.scrapers) {
      const endpoints = this.getSourceEndpoints(name);
      for (let endpoint of endpoints) {
        await scraper.dump(name, endpoint);
      }
    }
  }
}

/**
 * 信号量实现 - 用于控制并发数量
 */
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      if (this.permits > 0) {
        this.permits--;
        this.executeTask(task, resolve, reject);
      } else {
        this.waitQueue.push(() => {
          this.permits--;
          this.executeTask(task, resolve, reject);
        });
      }
    });
  }

  private async executeTask<T>(
    task: () => Promise<T>,
    resolve: (value: T) => void,
    reject: (reason?: any) => void,
  ) {
    try {
      const result = await task();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.release();
    }
  }

  private release() {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const next = this.waitQueue.shift()!;
      next();
    }
  }
}

// 类型定义
export interface ConcurrentScrapeResult {
  startTime: Date;
  endTime: Date;
  duration: number;
  totalSources: number;
  totalEndpoints: number;
  successCount: number;
  failedCount: number;
  sourceResults: Record<string, SourceScrapeResult>;
  summary: {
    totalTime: string;
    averageTimePerSource: string;
    successRate: string;
    sourcesCompleted: number;
    endpointsPerMinute: number;
  };
}

export interface SourceScrapeResult {
  source: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  totalEndpoints: number;
  successCount: number;
  failedCount: number;
  endpoints: Record<string, EndpointResult>;
  error?: string;
}

export interface EndpointResult {
  dataType: string;
  success: boolean;
  duration: number;
  taskId?: string;
  processedItems: number;
  error?: string;
}

export interface AllSourcesStats {
  sources: Record<string, any>;
  totalItems: number;
  totalTasks: number;
  generatedAt: Date;
}

export interface CrossSourceSearchResult {
  keyword: string;
  totalResults: number;
  sources: Record<
    string,
    {
      items: any[];
      count: number;
      error?: string;
    }
  >;
  aggregatedResults: any[];
  searchTime: number;
}

export interface HealthCheckResult {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  sources: Record<
    string,
    {
      status: 'healthy' | 'unhealthy';
      database: string;
      error?: string;
    }
  >;
  timestamp: Date;
}

export interface FlashScrapeResult {
  sourceResults: Record<string, FlashSourceResult>;
  successCount: number;
  failedCount: number;
  totalSources: number;
  summary: {
    totalTime: string;
    successRate: string;
    averageTimePerSource: string;
  };
}

export interface FlashSourceResult {
  source: string;
  success: boolean;
  duration: number;
  processedItems?: number;
  error?: string;
}
