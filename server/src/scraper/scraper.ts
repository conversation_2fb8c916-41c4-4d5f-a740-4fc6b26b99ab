import { MongoClient, Db, Collection } from 'mongodb';
import { logger } from '../utils/logger';
import * as pako from 'pako';
import fs from 'node:fs/promises'
import { createWriteStream } from 'node:fs';
import path from 'node:path';
import { createDateIterator, stringToHexHash } from '../utils/utils';

/**
 * 通用数据源接口
 */
export interface DataSourceItem {
  id: string;
  title: string;
  content?: string;
  description?: string;
  url?: string;
  publishTime: Date;
  source: string;
  dataType: string;
  tags?: string[];
  author?: string;
  originalData: any; // 保存原始API返回数据
  metadata?: {
    importance?: number;
    readCount?: number;
    likeCount?: number;
    shareCount?: number;
    [key: string]: any;
  };
  cover?: string
  TweetUrl?: string
  tweetText?: string
  sourceUrl?: string
  articleContent?: string
}

/**
 * 抓取任务状态
 */
export interface ScrapingTask {
  _id?: string;
  source: string;
  dataType: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  startTime?: Date;
  endTime?: Date;
  lastPageProcessed?: number;
  totalPages?: number;
  totalItems: number;
  processedItems: number;
  failedItems: number;
  errorMessage?: string;
  resumeData?: any;
  forceFullScrape?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * API端点配置
 */
export interface ApiEndpointConfig {
  url: string;
  method: 'GET' | 'POST';
  headers: Record<string, string>;
  params?: Record<string, any>;
  body?: any;
  bodyType?: 'json' | 'form';
  parser: string;
  paging?: {
    pageParam: string;
    sizeParam: string;
    defaultSize: number;
    maxPages?: number;
  };
}

/**
 * 数据源配置
 */
export interface DataSourceConfig {
  source: string;
  endpoints: Record<string, ApiEndpointConfig>;
}

/**
 * 增强型数据抓取器 - 基于实际API规范
 */
export class AccurateDataScraper {
  private db: Db;
  private itemsCollection: Collection<DataSourceItem>;
  private tasksCollection: Collection<ScrapingTask>;
  private configCollection: Collection<DataSourceConfig>;
  private MAX_FETCH_RETRY = 3;

  constructor(db: Db) {
    this.db = db;
    this.itemsCollection = this.db.collection('scraped_items');
    this.tasksCollection = this.db.collection('scraping_tasks');
    this.configCollection = this.db.collection('source_configs');

    this.createIndexes();
  }

  /**
   * 创建数据库索引
   */
  private async createIndexes(): Promise<void> {
    try {
      await Promise.all([
        this.itemsCollection.createIndex({ source: 1, dataType: 1 }),
        this.itemsCollection.createIndex({ publishTime: -1 }),
        this.itemsCollection.createIndex({ id: 1, source: 1 }, { unique: true }),
        this.tasksCollection.createIndex({ source: 1, dataType: 1 }),
        this.tasksCollection.createIndex({ status: 1 }),
        this.configCollection.createIndex({ source: 1 }, { unique: true })
      ]);
      logger.debug('数据库索引创建完成');
    } catch (error) {
      logger.warn('创建索引失败:', error);
    }
  }

  /**
   * 初始化精确的数据源配置
   */
  async initializeSourceConfigs(): Promise<void> {
    const configs: DataSourceConfig[] = [
      {
        source: 'chaincatcher',
        endpoints: {
          article: {
            url: 'https://www.chaincatcher.com/pc/content/page',
            method: 'POST',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'content-type': 'application/json',
              'language': 'zh-CN',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'ref': 'https://www.chaincatcher.com/article',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              'cookie': 'i18n_redirected=zh; auth.strategy=local; noticeTime=2025/7/30'
            },
            body: { type: 1, articleTypes: [1, 2, 6], pageNumber: 1, pageSize: 20 },
            bodyType: 'json',
            parser: 'chaincatcherParser',
            paging: { pageParam: 'pageNumber', sizeParam: 'pageSize', defaultSize: 20 }
          },
          flash: {
            url: 'https://www.chaincatcher.com/pc/content/page',
            method: 'POST',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'content-type': 'application/json',
              'language': 'zh-CN',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'ref': 'https://www.chaincatcher.com/news',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              'cookie': 'i18n_redirected=zh; auth.strategy=local; noticeTime=2025/7/30'
            },
            body: { type: 2, newsFlashTypes: [1, 2], pageNumber: 1, pageSize: 20 },
            bodyType: 'json',
            parser: 'chaincatcherParser',
            paging: { pageParam: 'pageNumber', sizeParam: 'pageSize', defaultSize: 20 }
          }
        }
      },
      {
        source: 'chainfeeds',
        endpoints: {
          feed: {
            url: 'https://api.chainfeeds.xyz/feed/list',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'd-token': 'null',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'Referer': 'https://www.chainfeeds.xyz/',
              'Origin': 'https://www.chainfeeds.xyz/',
              'sec-fetch-site': 'same-site',
              'tenant': '1'
            },
            params: { page: 1, page_size: 20, group_alias: 'selected' },
            parser: 'chainfeedsParser',
            paging: { pageParam: 'page', sizeParam: 'page_size', defaultSize: 20 }
          },
          flash: {
            url: 'https://api.chainfeeds.xyz/feed/list',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'd-token': 'null',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'Referer': 'https://www.chainfeeds.xyz/',
              'Origin': 'https://www.chainfeeds.xyz/',
              'sec-fetch-site': 'same-site',
              'tenant': '1'
            },
            params: { page: 1, page_size: 20, group_alias: 'flash' },
            parser: 'chainfeedsParser',
            paging: { pageParam: 'page', sizeParam: 'page_size', defaultSize: 20 }
          },
          topic: {
            url: 'https://api.chainfeeds.xyz/theme/list',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'd-token': 'null',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'Referer': 'https://www.chainfeeds.xyz/',
              'Origin': 'https://www.chainfeeds.xyz/',
              'sec-fetch-site': 'same-site',
              'tenant': '1'
            },
            params: { page: 1, page_size: 10, language: 'zh-cn' },
            parser: 'chainfeedsTopicParser',
            paging: { pageParam: 'page', sizeParam: 'page_size', defaultSize: 10 }
          },
          subject: {
            url: 'https://api.chainfeeds.xyz/subject/list',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'd-token': 'null',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'Referer': 'https://www.chainfeeds.xyz/',
              'Origin': 'https://www.chainfeeds.xyz/',
              'sec-fetch-site': 'same-site',
              'tenant': '1'
            },
            params: { page: 1, page_size: 10 },
            parser: 'chainfeedsSubjectParser',
            paging: { pageParam: 'page', sizeParam: 'page_size', defaultSize: 10 }
          }
        }
      },
      {
        source: 'followin',
        endpoints: {
          flash: {
            url: 'https://api.followin.io/feed/list/recommended/news',
            method: 'POST',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'content-type': 'application/json',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-bparam': '{"a":"web","b":"Windows","c":"zh-Hans","d":8,"e":"","f":"","g":"","h":"2.26.0","i":"official"}',
              'x-gtoken': 'sxen0h4eiVmZvl6ko7scX1kGFEYbBcB2cxHD7aHSlfr1MiMDOL0vyIneIqfcyiA_',
              'x-token': 'null',
              'Referer': 'https://followin.io/'
            },
            body: { only_important: false, count: 20, page: 1, last_cursor: null, last_source: 'algo' },
            bodyType: 'json',
            parser: 'followinParser',
            paging: { pageParam: 'page', sizeParam: 'count', defaultSize: 20 }
          }
        }
      },
      {
        source: 'odaily',
        endpoints: {
          depth: {
            url: 'https://web-api.odaily.news/depth/page',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'authorization': 'Bearer undefined',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-locale': 'zh-CN',
              'Referer': 'https://www.odaily.news/deep',
              'Origin': 'https://www.odaily.news'
            },
            params: { page: 1, size: 16, sortType: 'latest' },
            parser: 'odailyParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 16 }
          },
          flash: {
            url: 'https://web-api.odaily.news/newsflash/page',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'authorization': 'Bearer undefined',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-locale': 'zh-CN',
              'Referer': 'https://www.odaily.news/newsflash',
              'Origin': 'https://www.odaily.news'
            },
            params: { page: 1, size: 16, isImport: false, groupId: 0 },
            parser: 'odailyParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 16 }
          },
          post: {
            url: 'https://web-api.odaily.news/post/page',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              'cookie': 'UC_SESSION=jKvymdtwYyhA7FBKGkWudNUxw5xcpoUuHCogPXJy',
              'Referer': 'https://www.odaily.news/',
              'Origin': 'https://www.odaily.news'
            },
            params: { page: 1, size: 16 },
            parser: 'odailyParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 16 }
          },
          price: {
            url: 'https://web-api.odaily.news/market/listBySymbols',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'authorization': 'Bearer undefined',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-locale': 'zh-CN',
              'Referer': 'https://www.odaily.news/tool',
              'Origin': 'https://www.odaily.news'
            },
            params: { 'symbols[]': ['btcusdt', 'ethusdt', 'htxusdt', 'solusdt', 'bnbusdt'], exchange: 1 },
            parser: 'odailyPriceParser'
          },
          tweet: {
            url: 'https://web-api.odaily.news/viewpoint/page',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'authorization': 'Bearer undefined',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-locale': 'zh-CN',
              'Referer': 'https://www.odaily.news/viewpoint',
              'Origin': 'https://www.odaily.news'
            },
            params: { page: 1, size: 16 },
            parser: 'odailyTweetParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 16 }
          }
        }
      },
      {
        source: 'techflow',
        endpoints: {
          flash: {
            url: 'https://www.techflowpost.com/ashx/newflash_index.ashx',
            method: 'POST',
            headers: {
              'accept': '*/*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
              'pragma': 'no-cache',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              'x-requested-with': 'XMLHttpRequest'
            },
            body: 'pageindex=1&pagesize=10&ncata_id=&is_hot=N&max_id=91139',
            bodyType: 'form',
            parser: 'techflowParser',
            paging: { pageParam: 'pageindex', sizeParam: 'pagesize', defaultSize: 10 }
          }
        }
      },
      {
        source: 'trendx',
        endpoints: {
          financing: {
            url: 'https://www.trendx.tech/v1/dao/public/projects/financing',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              'x-authorization': '',
              'x-chain-id': '56',
              'cookie': '__cf_bm=50ITThd4k_Y4OxZZSuGvykZCuH.iAPTAwxMLWbkkoVY-1753866901-1.0.1.1-VpzfYfIqodCr61izZkzpwVV9ej6PlR8eyLV0qv6XCTuQirWtOlfHLgyXU9vk31bg3j.7IU.DUs539ADAazn5OU1CtqttaCMSMYUzYBKxnQw; _cfuvid=Cyt215bCeAezuZe0l_y67i5f1Vzx3ZVLrVfNKB.gdvw-1753866901628-0.0.1.1-604800000; acw_tc=0a0f6b7617538669020914902e4f68c446af9daf1aa1544dcc43dd730b3864; local-chain-id=56; locale=zh-cn; theme=light; SERVERID=13d26951d06e361f0a39e81f001d2ba1|1753866963|1753866902; SERVERCORSID=13d26951d06e361f0a39e81f001d2ba1|1753866963|1753866902',
              'Referer': 'https://www.trendx.tech/zh-cn/financing',
              'Origin': 'https://www.trendx.tech'
            },
            params: { projectName: '', page: 1, rows: 20 },
            parser: 'trendxParser',
            paging: { pageParam: 'page', sizeParam: 'rows', defaultSize: 20 }
          },
          news: {
            url: 'https://www.trendx.tech/v1/dao/public/news/articles',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              'x-authorization': '',
              'x-chain-id': '56',
              'cookie': '__cf_bm=50ITThd4k_Y4OxZZSuGvykZCuH.iAPTAwxMLWbkkoVY-1753866901-1.0.1.1-VpzfYfIqodCr61izZkzpwVV9ej6PlR8eyLV0qv6XCTuQirWtOlfHLgyXU9vk31bg3j.7IU.DUs539ADAazn5OU1CtqttaCMSMYUzYBKxnQw; _cfuvid=Cyt215bCeAezuZe0l_y67i5f1Vzx3ZVLrVfNKB.gdvw-1753866901628-0.0.1.1-604800000; acw_tc=0a0f6b7617538669020914902e4f68c446af9daf1aa1544dcc43dd730b3864; local-chain-id=56; locale=zh-cn; theme=light; SERVERID=13d26951d06e361f0a39e81f001d2ba1|1753866963|1753866902; SERVERCORSID=13d26951d06e361f0a39e81f001d2ba1|1753866963|1753866902',
              'Referer': 'https://www.trendx.tech/zh-cn/news',
              'Origin': 'https://www.trendx.tech'
            },
            params: { page: 1, rows: 100 },
            parser: 'trendxParser',
            paging: { pageParam: 'page', sizeParam: 'rows', defaultSize: 100 }
          },
          tweet: {
            url: 'https://www.trendx.tech/v1/dao/public/topics/388/tweets',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              'x-authorization': '',
              'x-chain-id': '56',
              'cookie': '__cf_bm=50ITThd4k_Y4OxZZSuGvykZCuH.iAPTAwxMLWbkkoVY-1753866901-1.0.1.1-VpzfYfIqodCr61izZkzpwVV9ej6PlR8eyLV0qv6XCTuQirWtOlfHLgyXU9vk31bg3j.7IU.DUs539ADAazn5OU1CtqttaCMSMYUzYBKxnQw; _cfuvid=Cyt215bCeAezuZe0l_y67i5f1Vzx3ZVLrVfNKB.gdvw-1753866901628-0.0.1.1-604800000; acw_tc=0a0f6b7617538669020914902e4f68c446af9daf1aa1544dcc43dd730b3864; local-chain-id=56; locale=zh-cn; theme=light; SERVERID=13d26951d06e361f0a39e81f001d2ba1|1753866963|1753866902; SERVERCORSID=13d26951d06e361f0a39e81f001d2ba1|1753866963|1753866902',
              'Referer': 'https://www.trendx.tech/zh-cn/topic/388',
              'Origin': 'https://www.trendx.tech'
            },
            params: { page: 1, rows: 15 },
            parser: 'trendxTweetParser',
            paging: { pageParam: 'page', sizeParam: 'rows', defaultSize: 15 }
          }
        }
      },
      {
        source: 'theblockbeats',
        endpoints: {
          flash: {
            url: 'https://api.blockbeats.cn/v2/newsflash/list',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'lang': 'cn',
              'pragma': 'no-cache',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'cross-site',
              'token': '',
              'Referer': 'https://www.theblockbeats.info/',
              'Referrer-Policy': 'strict-origin-when-cross-origin'
            },
            params: { page: 1, limit: 10, ios: -2, detective: -2 },
            parser: 'theblockbeatsParser',
            paging: { pageParam: 'page', sizeParam: 'limit', defaultSize: 10 }
          },
          finance: {
            url: 'https://api.blockbeats.cn/v2/financing/list',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'lang': 'cn',
              'pragma': 'no-cache',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'cross-site',
              'token': '',
              'Referer': 'https://www.theblockbeats.info/finance',
              'Referrer-Policy': 'strict-origin-when-cross-origin'
            },
            params: { page: 1, limit: 10, start_time: '', end_time: '', min_money: '', max_money: '', title: '' },
            parser: 'theblockbeatsFinanceParser',
            paging: { pageParam: 'page', sizeParam: 'limit', defaultSize: 10 }
          }
        }
      },
      {
        source: 'panewslab',
        endpoints: {
          flash: {
            url: 'https://api.panewslab.com/webapi/flashnews',
            method: 'GET',
            headers: {
              'accept': '*/*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'Referer': 'https://www.panewslab.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin'
            },
            params: { rn: 20, lid: 1, apppush: 0 },
            parser: 'panewslabParser'
          }
        }
      },
      {
        source: 'foresightnews',
        endpoints: {
          dayNews: {
            url: 'https://api.foresightnews.pro/v1/dayNews',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-requested-with': 'XMLHttpRequest',
              // 'cookie': 'ssxmod_itna=Qui=1GCb0KiIxYQiQo0=GR3tD=tG77DwxBP017sDuxiK08D6BDBR0QDtjmTFkTCDGxB+oyUr8D5bD8+eibthKZgT5Q9cu2X38xFGSWmtPD',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro',
              'Referrer-Policy': 'strict-origin-when-cross-origin'
            },
            params: { date: '20250730' },
            parser: 'foresightnewsParser'
          },
          news: {
            url: 'https://api.foresightnews.pro/v1/news',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-requested-with': 'XMLHttpRequest',
              // 'cookie': 'ssxmod_itna=Qui=1GCb0KiIxYQiQo0=GR3tD=tG77DwxBP017sDuxiK08D6BDBR0QDtjmTFkTCDGxB+oyUr8D5bD8+eibthKZgT5Q9cu2X38xFGSWmtPD',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro',
              'Referrer-Policy': 'strict-origin-when-cross-origin'
            },
            params: { page: 1, size: 20 },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 20 }
          },
          feed: {
            url: 'https://api.foresightnews.pro/v2/feed',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-requested-with': 'XMLHttpRequest',
              // 'cookie': 'ssxmod_itna=Qui=1GCb0KiIxYQiQo0=GR3tD=tG77DwxBP017sDuxiK08D6BDBR0QDtjmTFkTCDGxB+oyUr8D5bD8+eibthKZgT5Q9cu2X38xFGSWmtPD',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro',
              'Referrer-Policy': 'strict-origin-when-cross-origin'
            },
            params: { page: 1, size: 20 },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 20 }
          },
          event: {
            url: 'https://api.foresightnews.pro/v1/events',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro'
            },
            params: { page: 1, size: 20 },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 20 }
          },
          topic: {
            url: 'https://api.foresightnews.pro/v1/topics',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro'
            },
            params: { page: 1, size: 20 },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 20 }
          },
          column: {
            url: 'https://api.foresightnews.pro/v1/columns',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro'
            },
            params: { page: 1, size: 20, search: '', is_hot: true },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 20 }
          },
          article: {
            url: 'https://api.foresightnews.pro/v1/articles',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro'
            },
            params: { page: 1, size: 20, column_id: 721 },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 20 }
          },
          tools: {
            url: 'https://api.foresightnews.pro/v1/links2',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro'
            },
            params: { page: 1, size: 18 },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 18 }
          },
          fundraising: {
            url: 'https://api.foresightnews.pro/v1/fundraising',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro'
            },
            params: { page: 1, size: 20, search: '', sort_by: '', sort: '', min_amount: '', max_amount: '', round: '', start_time: '', end_time: '' },
            parser: 'foresightnewsParser',
            paging: { pageParam: 'page', sizeParam: 'size', defaultSize: 20 }
          },
          calendars: {
            url: 'https://api.foresightnews.pro/v1/calendars',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro'
            },
            params: { month_date: '20250801' },
            parser: 'foresightnewsParser'
          },
          articleDetail: {
            url: 'https://api.foresightnews.pro/v1/article/{id}',
            method: 'GET',
            headers: {
              'accept': 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'cache-control': 'no-cache',
              'pragma': 'no-cache',
              'priority': 'u=1, i',
              'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              'x-requested-with': 'XMLHttpRequest',
              'Referer': 'https://foresightnews.pro/',
              'Origin': 'https://foresightnews.pro',
              'Referrer-Policy': 'strict-origin-when-cross-origin'
            },
            parser: 'foresightnewsParser'
          },
        }
      }
    ];

    // 保存配置到数据库
    for (const config of configs) {
      await this.configCollection.updateOne(
        { source: config.source },
        { $set: config },
        { upsert: true }
      );
    }

    logger.info(`初始化了 ${configs.length} 个数据源的完整配置`);
  }

  /**
   * 开始抓取任务（支持断点续传）
   */
  async startScraping(
    source: string,
    dataType: string,
    options: {
      forceFullScrape?: boolean;
      maxPages?: number;
      resume?: boolean;
    } = {}
  ): Promise<ScrapingTask> {
    // 查找或创建任务
    let task = await this.tasksCollection.findOne({
      source,
      dataType,
      status: { $in: ['pending', 'running', 'paused'] }
    });

    if (task && !options.resume && !options.forceFullScrape) {
      // 通过内容校验来进行增量更新
      // throw new Error(`任务已存在且正在执行中: ${source}-${dataType}`);
    }

    if (!task || options.forceFullScrape) {
      // 创建新任务
      const newTask: Omit<ScrapingTask, '_id'> = {
        source,
        dataType,
        status: 'pending',
        totalItems: 0,
        processedItems: 0,
        failedItems: 0,
        forceFullScrape: options.forceFullScrape || false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await this.tasksCollection.insertOne(newTask);
      task = { ...newTask, _id: result.insertedId.toString() };
    }

    if (!task) {
      throw new Error('无法创建或获取任务');
    }

    // 开始执行任务
    await this.executeScrapingTask(task, options);
    return task;
  }

  /**
   * 执行抓取任务
   */
  private async executeScrapingTask(
    task: ScrapingTask,
    options: { maxPages?: number } = {}
  ): Promise<void> {
    try {
      // 更新任务状态
      await this.tasksCollection.updateOne(
        { _id: task._id },
        {
          $set: {
            status: 'running',
            startTime: new Date(),
            updatedAt: new Date()
          }
        }
      );

      // 获取数据源配置
      const config = await this.configCollection.findOne({ source: task.source });
      if (!config || !config.endpoints[task.dataType]) {
        throw new Error(`未找到数据源配置: ${task.source}-${task.dataType}`);
      }

      const endpoint = config.endpoints[task.dataType];
      let currentPage = task.lastPageProcessed || 1;
      let hasMore = true;
      let processedCount = task.processedItems || 0;
      let failedCount = task.failedItems || 0;

      // 获取最新数据的时间戳，用于增量抓取
      let lastItemTime: Date | null = null;
      if (!task.forceFullScrape) {
        const latestItem = await this.itemsCollection.findOne(
          { source: task.source, dataType: task.dataType },
          { sort: { publishTime: -1 } }
        );
        lastItemTime = latestItem?.publishTime || null;
      }

      logger.info(`开始抓取 ${task.source}-${task.dataType}, 从第 ${currentPage} 页开始`);
      const today = new Date().toISOString().split('T')[0].replace(/-/gim, '')
      const startDate = today || "20240101"
      const nextYearDay = new Date(Date.now() + 365 * 24 * 60 * 60 * 1e3).toISOString().split('T')[0].replace(/-/gim, '')
      let dateIter = createDateIterator(startDate, today)
      let isDateIter = task.source === 'foresightnews' && (task.dataType === 'calendars' || task.dataType === 'dayNews')
      if (task.source === 'foresightnews' && task.dataType === 'calendars') {
        dateIter = createDateIterator(startDate, nextYearDay)
      }
      let panewslabNextDate = ""
      while (hasMore && (!options.maxPages || currentPage <= options.maxPages)) {
        try {
          logger.debug(`正在抓取 ${task.source} ${task.dataType} 第 ${currentPage} 页...`);

          // 构建请求参数
          const requestData = await this.buildRequest(endpoint, currentPage);
          if (isDateIter) {
            const nextDate = dateIter.next()
            if (!nextDate) break
            const searchParams = new URLSearchParams();
            searchParams.append('month_date', nextDate)
            requestData.url = endpoint.url + '?' + searchParams.toString();
            logger.debug(`fetch foresightnews ${task.dataType}: ${nextDate}`)
          }
          if (task.source === 'panewslab') {
            const searchParams = new URLSearchParams();
            searchParams.append('rn', "20")
            searchParams.append('lid', "1")
            searchParams.append('apppush', "0")
            if (panewslabNextDate) {
              searchParams.append('lastTime', panewslabNextDate)
            }
            requestData.url = endpoint.url + '?' + searchParams.toString();
            logger.debug(`fetch panewslab ${task.dataType}: ${panewslabNextDate}`)
          }
          // 发送请求
          const response = await fetch(requestData.url, requestData.options);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          let data = await response.json();

          // 处理ForesightNews压缩数据
          if (task.source === 'foresightnews') {
            data = this.parseCompressedProperties(data);
          }

          // 解析数据
          const items = await this.parseResponseData(data, task.source, task.dataType, endpoint.parser);

          if (!items || items.length === 0) {
            if (isDateIter) continue
            logger.debug(`第 ${currentPage} 页没有数据，停止抓取`);
            hasMore = false;
            break;
          }
          if (task.source === 'panewslab') {
            const nextDate = items[items.length - 1]?.publishTime
            if (nextDate) {
              panewslabNextDate = Math.floor(new Date(nextDate).getTime() / 1e3) + ""
            }
          }

          // 检查是否为增量抓取且遇到已存在的数据
          if (lastItemTime && !task.forceFullScrape) {
            // 基于时间和ID的双重去重机制
            const existingIds = await this.getExistingItemIds(task.source, task.dataType, items.map(item => item.id));
            const newItems = items.filter(item =>
              item.publishTime > lastItemTime! && !existingIds.has(item.id)
            );

            if (newItems.length === 0) {
              logger.info(`${task.source}-${task.dataType} 增量抓取完成，第 ${currentPage} 页的数据都已存在`);
              hasMore = false;
              break;
            }

            // 只保存新数据
            await this.saveItems(task, newItems);
            processedCount += newItems.length;
          } else {
            // 全量抓取，过滤重复数据
            const existingIds = await this.getExistingItemIds(task.source, task.dataType, items.map(item => item.id));
            const uniqueItems = items.filter(item => !existingIds.has(item.id));

            await this.saveItems(task, uniqueItems);
            processedCount += uniqueItems.length;
          }

          // 更新任务进度
          await this.tasksCollection.updateOne(
            { _id: task._id },
            {
              $set: {
                lastPageProcessed: currentPage,
                processedItems: processedCount,
                failedItems: failedCount,
                updatedAt: new Date()
              }
            }
          );

          currentPage++;

          // 添加延迟以避免过于频繁的请求
          await this.delay(1000);

        } catch (error) {
          logger.error(`抓取 ${task.source} ${task.dataType} 第 ${currentPage} 页...`);
          logger.error(`抓取第 ${currentPage} 页失败:`, error);
          failedCount++;

          // 如果连续失败多次，停止抓取
          if (failedCount > this.MAX_FETCH_RETRY) {
            throw error;
          }

          currentPage++;
        }
      }

      // 任务完成
      await this.tasksCollection.updateOne(
        { _id: task._id },
        {
          $set: {
            status: 'completed',
            endTime: new Date(),
            totalPages: currentPage - 1,
            processedItems: processedCount,
            failedItems: failedCount,
            updatedAt: new Date()
          }
        }
      );

      logger.info(`抓取完成: ${task.source}-${task.dataType}, 处理 ${processedCount} 项，失败 ${failedCount} 项`);

    } catch (error) {
      // 任务失败
      await this.tasksCollection.updateOne(
        { _id: task._id },
        {
          $set: {
            status: 'failed',
            endTime: new Date(),
            errorMessage: (error as any)?.message || '未知错误',
            updatedAt: new Date()
          }
        }
      );

      logger.error(`抓取任务失败: ${task.source}-${task.dataType}:`, error);
      throw error;
    }
  }

  /**
   * 构建请求
   */
  private async buildRequest(endpoint: ApiEndpointConfig, page: number): Promise<{ url: string; options: RequestInit }> {
    const options: RequestInit = {
      method: endpoint.method,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        ...endpoint.headers
      }
    };

    let url = endpoint.url;
    let params = { ...endpoint.params };
    let body = endpoint.body ? { ...endpoint.body } : null;

    // 设置分页参数
    if (endpoint.paging) {
      if (endpoint.method === 'GET') {
        params[endpoint.paging.pageParam] = page;
      } else if (body) {
        body[endpoint.paging.pageParam] = page;
      }
    }

    // 处理 GET 请求的参数
    if (endpoint.method === 'GET' && params) {
      const searchParams = new URLSearchParams();
      for (const [key, value] of Object.entries(params)) {
        if (Array.isArray(value)) {
          // 处理数组参数，如 symbols[]
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.append(key, String(value));
        }
      }
      url += '?' + searchParams.toString();
    }

    // 处理 POST 请求的 body
    if (endpoint.method === 'POST' && body) {
      if (endpoint.bodyType === 'form') {
        // 表单数据
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(body)) {
          formData.append(key, String(value));
        }
        options.body = formData.toString();
      } else {
        // JSON 数据
        options.body = JSON.stringify(body);
        if (!options.headers) options.headers = {};
        (options.headers as any)['content-type'] = 'application/json';
      }
    }

    return { url, options };
  }

  /**
   * 解析响应数据 - 基于实际API规范
   */
  private async parseResponseData(
    data: any,
    source: string,
    dataType: string,
    parser: string
  ): Promise<DataSourceItem[]> {
    const items: DataSourceItem[] = [];

    try {
      switch (parser) {
        case 'chaincatcherParser':
          if (data.data?.items) {
            for (const item of data.data.items) {
              items.push({
                id: `${source}-${item.id}`,
                title: item.title,
                content: item.digest || item.description,
                url: item.url,
                publishTime: new Date(item.releaseTime || item.createTime),
                source,
                dataType,
                tags: item.tagNames || [],
                originalData: item
              });
            }
          }
          break;

        case 'chainfeedsParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-${item.uuid}`,
                title: item.title,
                content: item.abstract,
                url: item.source_url,
                publishTime: new Date(item.show_time),
                source,
                dataType,
                author: item.author_name,
                tags: item.tags?.map((tag: any) => tag.tag_name) || [],
                metadata: {
                  readCount: item.view_count
                },
                originalData: item
              });
            }
          }
          break;

        case 'chainfeedsTopicParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-topic-${item.theme_id}`,
                title: item.title,
                content: item.abstract,
                url: item.source_url,
                publishTime: new Date(item.show_time),
                source,
                dataType,
                tags: [],
                metadata: {
                  articleNum: item.article_num,
                  score: item.score
                },
                originalData: item
              });
            }
          }
          break;

        case 'chainfeedsSubjectParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-subject-${item.uuid}`,
                title: item.title,
                content: item.abstract,
                publishTime: new Date(item.show_time),
                source,
                dataType,
                tags: item.tags || [],
                originalData: item
              });
            }
          }
          break;

        case 'followinParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-${item.id}`,
                title: item.title,
                content: item.content,
                url: item.source_url,
                publishTime: new Date(item.publish_time),
                source,
                dataType,
                author: item.nickname,
                tags: item.tags?.map((tag: any) => tag.name) || [],
                metadata: {
                  readCount: item.views,
                  likeCount: item.likes,
                  shareCount: item.shares
                },
                originalData: item
              });
            }
          }
          break;

        case 'odailyParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-${item.id}`,
                title: item.title,
                content: item.description || item.summary,
                url: item.newsUrl,
                publishTime: new Date(item.publishTimestamp || item.publishTime),
                source,
                dataType,
                tags: item.tags?.map((tag: any) => tag.name) || [],
                originalData: item,
                cover: item.cover,
              });
            }
          }
          break;

        case 'odailyPriceParser':
          if (data.data && Array.isArray(data.data)) {
            for (const item of data.data) {
              items.push({
                id: `${source}-price-${item.symbol}`,
                title: `${item.name} (${item.symbol.toUpperCase()})`,
                content: `当前价格: ${item.price}, 24h涨跌: ${item.rate24H}`,
                publishTime: new Date(),
                source,
                dataType,
                metadata: {
                  price: item.price,
                  rate24H: item.rate24H,
                  isUp: item.isUp
                },
                originalData: item
              });
            }
          }
          break;

        case 'odailyTweetParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-tweet-${item.id}`,
                title: item.name,
                content: item.summary,
                url: item.entityUrl,
                publishTime: new Date(item.publishTime || Date.now()),
                source,
                dataType,
                author: item.name,
                originalData: item,
                cover: item.cover,
                TweetUrl: item.entityUrl,
              });
            }
          }
          break;

        case 'techflowParser':
          if (data.content && Array.isArray(data.content)) {
            for (const item of data.content) {
              items.push({
                id: `${source}-${item.nnewflash_id}`,
                title: item.stitle,
                content: item.sabstract,
                url: item.surl,
                publishTime: new Date(item.dcreate_time),
                source,
                dataType,
                author: item.screate_person,
                sourceUrl: item.surl,
                originalData: item
              });
            }
          }
          break;

        case 'trendxParser':
          if (data.data?.rows) {
            for (const item of data.data.rows) {
              let title = item.title || item.fullName;
              let content = item.summary || item.brief;
              let publishTime = new Date(item.createAt || item.publishTime || Date.now());

              if (dataType === 'financing') {
                title = item.fullName;
                content = `融资轮次: ${item.round}, 金额: ${item.amount}, 投资方: ${item.investors}`;
              }

              items.push({
                id: `${source}-${item.id}`,
                title,
                content,
                url: item.linkUrl || item.url,
                publishTime,
                source,
                dataType,
                originalData: item,
                cover: item.sourceImageUrl
              });
            }
          }
          break;

        case 'trendxTweetParser':
          if (data.data?.rows) {
            for (const item of data.data.rows) {
              items.push({
                id: `${source}-tweet-${item.tweetId}`,
                title: `@${item.twitterName}`,
                content: item.tweetText,
                publishTime: new Date(item.tweetCreateTime * 1e3),
                source,
                dataType,
                author: item.twitterName,
                originalData: item,
                tweetText: item.tweetText,
                TweetUrl: item.tweetUrl,
              });
            }
          }
          break;

        case 'theblockbeatsParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-${item.id}`,
                title: item.title,
                content: item.content,
                publishTime: new Date(item.add_time * 1000),
                source,
                dataType,
                tags: item.tag_list || [],
                originalData: item,
                cover: item.img_url
              });
            }
          }
          break;

        case 'theblockbeatsFinanceParser':
          if (data.data?.list) {
            for (const item of data.data.list) {
              items.push({
                id: `${source}-finance-${item.id}`,
                title: item.title,
                content: `领域: ${item.field}, 金额: ${item.money}, 投资方: ${item.invest}`,
                publishTime: item.time,
                source,
                dataType,
                metadata: {
                  field: item.field,
                  money: item.money,
                  invest: item.invest
                },
                originalData: item
              });
            }
          }
          break;

        case 'panewslabParser':
          if (data.data?.flashNews) {
            for (const dateGroup of data.data.flashNews) {
              for (const item of dateGroup.list) {
                items.push({
                  id: `${source}-${item.id}`,
                  title: item.title,
                  content: item.desc,
                  publishTime: new Date(item.publishTime * 1000),
                  source,
                  dataType,
                  author: item.author?.name,
                  originalData: item,
                  cover: item?.author?.img
                });
              }
            }
          }
          if (data.data?.hotArticles) {
            for (const item of data.data.hotArticles) {
              items.push({
                id: `${source}-${item.id}`,
                title: item.title,
                publishTime: new Date(item.publishTime * 1000),
                source,
                dataType,
                originalData: item,
              });
            }
          }
          break;

        case 'foresightnewsParser':
          // ForesightNews 需要特殊处理压缩数据
          const processedData = this.parseCompressedProperties(data);
          if (processedData.data) {
            const list = processedData.data.list || processedData.data;
            if (Array.isArray(list)) {
              for (const item of list) {
                let key = item.id
                if (!key) key = stringToHexHash(item.title + item.description)
                if (!key) key = stringToHexHash(JSON.stringify(item))
                let time = (item.published_at || item.created_at || item.publish_time || item.start_time || item.end_time)
                if (String(time)?.length < 13) time = time * 1e3
                let publishTime = new Date(time)
                if (publishTime.toString() === 'Invalid Date') publishTime = new Date()

                let title = item.title
                let content = item.content || item.brief || item.summary
                let description = item.description || item.desc
                let sourceUrl = item.source_link
                let cover = item?.img
                let url = item?.url
                let tags = item.tags?.map((tag: any) => tag.name) || []
                let originalData = item
                let id = `${source}-${key}`
                if (dataType === 'feed') {
                  const detail = item?.source_type
                  title = item[detail]?.title
                  description = item[detail]?.brief
                  content = item[detail]?.content
                  sourceUrl = item[detail]?.source_link
                  cover = item[detail]?.img
                  originalData = item[detail]
                }
                if (dataType === 'articleDetail') {
                  id = `${source}-${dataType}-${item.id}`
                  description = item?.brief
                }
                items.push({
                  id,
                  title,
                  content,
                  description,
                  url,
                  publishTime,
                  source,
                  dataType,
                  tags,
                  originalData,
                  cover,
                  sourceUrl,
                });
              }
            }
          }
          break;

        default:
          logger.warn(`未知的解析器: ${parser}`);
      }
    } catch (error) {
      logger.error(`解析数据失败 (${source}-${dataType}-${parser}):`, error);
    }

    return items;
  }

  /**
   * 获取已存在的数据项ID集合
   */
  private async getExistingItemIds(source: string, dataType: string, itemIds: string[]): Promise<Set<string>> {
    const existingItems = await this.itemsCollection.find(
      {
        source,
        dataType,
        id: { $in: itemIds }
      },
      { projection: { id: 1 } }
    ).toArray();

    return new Set(existingItems.map(item => item.id));
  }

  /**
   * 保存数据项（改进版）
   */
  private async saveItems(task: ScrapingTask, items: DataSourceItem[]): Promise<void> {
    if (items.length === 0) return;

    const bulkOps = items.map(item => ({
      updateOne: {
        filter: { id: item.id },
        update: { $set: { ...item, updatedAt: new Date() } },
        upsert: true
      }
    }));

    try {
      await this.itemsCollection.bulkWrite(bulkOps);
      logger.debug(`保存了 ${task.source} ${items.length} 个数据项`);
    } catch (error) {
      logger.error(`批量保存数据失败:`, error);
      // 如果批量操作失败，尝试逐个保存
      for (const item of items) {
        try {
          await this.itemsCollection.updateOne(
            { id: item.id },
            { $set: { ...item, updatedAt: new Date() } },
            { upsert: true }
          );
        } catch (singleError) {
          logger.warn(`保存单个数据项失败 ${item.id}:`, singleError);
        }
      }
    }
  }

  /**
   * 处理ForesightNews压缩数据
   */
  private parseCompressedProperties<T>(obj: T): T {
    if (obj === null || obj === undefined) return obj;

    if (typeof obj === 'string' && obj.length > 0 && obj.startsWith('eJ')) {
      try {
        const binaryString = atob(obj);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        const decompressed = pako.inflate(bytes, { to: 'string' });
        return JSON.parse(decompressed) as T;
      } catch (error) {
        logger.warn('解析压缩字符串失败:', error);
        return obj;
      }
    }

    if (Array.isArray(obj)) {
      obj.forEach((item, index) => {
        obj[index] = this.parseCompressedProperties(item);
      });
      return obj;
    }

    if (typeof obj === 'object') {
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          obj[key] = this.parseCompressedProperties(obj[key]);
        }
      }
      return obj;
    }

    return obj;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(source: string, dataType: string): Promise<ScrapingTask | null> {
    return await this.tasksCollection.findOne(
      { source, dataType },
      { sort: { updatedAt: -1 } }
    );
  }

  /**
   * 获取所有任务
   */
  async getAllTasks(): Promise<ScrapingTask[]> {
    return await this.tasksCollection.find({}).sort({ updatedAt: -1 }).toArray();
  }

  /**
   * 暂停任务
   */
  async pauseTask(taskId: string): Promise<void> {
    await this.tasksCollection.updateOne(
      { _id: taskId },
      { $set: { status: 'paused', updatedAt: new Date() } }
    );
  }

  /**
   * 恢复任务
   */
  async resumeTask(taskId: string): Promise<void> {
    const task = await this.tasksCollection.findOne({ _id: taskId });
    if (task) {
      await this.executeScrapingTask(task);
    }
  }

  /**
   * 获取抓取统计
   */
  async getStats(): Promise<any> {
    const totalItems = await this.itemsCollection.countDocuments();
    const totalTasks = await this.tasksCollection.countDocuments();

    const statsBySource = await this.itemsCollection.aggregate([
      {
        $group: {
          _id: { source: '$source', dataType: '$dataType' },
          count: { $sum: 1 },
          latestItem: { $max: '$publishTime' }
        }
      }
    ]).toArray();

    const taskStats = await this.tasksCollection.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]).toArray();

    return {
      totalItems,
      totalTasks,
      itemsBySource: statsBySource,
      tasksByStatus: taskStats,
      generatedAt: new Date()
    };
  }

  /**
   * 清理旧数据
   */
  async cleanupOldData(daysToKeep: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const result = await this.itemsCollection.deleteMany({
      publishTime: { $lt: cutoffDate }
    });

    logger.info(`清理了 ${result.deletedCount} 条过期数据`);
  }
  /**
   * 删除 tasksCollection
   */
  async deleteTasksCollection(): Promise<void> {
    const result = await this.tasksCollection.deleteMany({});
    logger.info(`清理了 ${result.deletedCount} 条tasksCollection数据`);
  }
  async dump(name: string, dataType: string): Promise<void> {

    const data = await this.itemsCollection.find({ dataType }).toArray();
    const jsonData = JSON.stringify(data, null, 2);
    const filename = `${name}.${dataType}.json`
    const today = new Date().toISOString().split('T')[0]
    const outputFilePath = path.resolve(__dirname, '..', '..', `dump.${today}`, filename)
    await fs.mkdir(path.dirname(outputFilePath), { recursive: true })
    await fs.writeFile(outputFilePath, jsonData, 'utf8');
    console.log(`Exported ${data.length} documents to ${outputFilePath}`);
  }
  async dumpStream(name: string, dataType: string): Promise<void> {
    const cursor = this.itemsCollection.find({ dataType }).stream();
    const today = new Date().toISOString().split('T')[0];
    const filename = `${name}.${dataType}.json`;
    const outputFilePath = path.resolve(__dirname, '..', '..', `dump.${today}`, filename);
    await fs.mkdir(path.dirname(outputFilePath), { recursive: true });
  
    const writeStream = createWriteStream(outputFilePath, { encoding: 'utf8' });
    writeStream.write('[\n');
  
    let isFirst = true;
    let count = 0;
  
    for await (const doc of cursor) {
      if (!isFirst) writeStream.write(',\n');
      writeStream.write(JSON.stringify(doc, null, 2));
      isFirst = false;
      count++;
    }
  
    writeStream.write('\n]');
    writeStream.end();
  
    await new Promise((resolve, reject) => {
      writeStream.on('finish', () => {
        console.log(`Exported ${count} documents to ${outputFilePath}`);
        resolve(true);
      });
      writeStream.on('error', reject);
    });
  }

  /**
   * 抓取指定源的文章详情
   * @param source 数据源名称
   */
  async scrapeArticleDetails(source: string): Promise<void> {
    if (source !== 'foresightnews') {
      throw new Error(`暂不支持 ${source} 的文章详情抓取`);
    }

    logger.info(`开始抓取 ${source} 的文章详情`);

    try {
      // 1. 查询数据库中 dataType 为 article 的项
      const articles = await this.itemsCollection.find({
        source: source,
        dataType: 'article'
      }).toArray();

      logger.info(`找到 ${articles.length} 篇文章需要抓取详情`);

      if (articles.length === 0) {
        logger.warn('没有找到需要抓取详情的文章');
        return;
      }

      // 2. 获取 articleDetail 端点配置（从默认数据库获取）
      const defaultClient = new MongoClient(process.env.MONGODB_URL || '****************************************************************************************');
      await defaultClient.connect();
      const defaultDb = defaultClient.db(process.env.MONGODB_DB_NAME || 'chainmix');
      const defaultConfigCollection = defaultDb.collection('source_configs');

      const config = await defaultConfigCollection.findOne({ source: source });
      await defaultClient.close();

      if (!config || !config.endpoints.articleDetail) {
        throw new Error(`未找到 ${source} 的 articleDetail 端点配置`);
      }

      const endpoint = config.endpoints.articleDetail;
      let successCount = 0;
      let errorCount = 0;

      // 3. 遍历每篇文章，获取详情
      for (const article of articles) {
        try {
          if (article.articleContent) {
            logger.debug(`文章详情articleContent已存在，跳过: ${article.id}`);
            continue;
          }
          const articleId = article.originalData.id;
          if (!articleId) {
            logger.warn(`文章缺少 ID，跳过: ${article.title}`);
            continue;
          }

          // 检查是否已经抓取过详情
          const existingDetail = await this.itemsCollection.findOne({
            source: source,
            dataType: 'articleDetail',
            id: articleId
          });

          if (existingDetail) {
            logger.debug(`文章详情已存在，跳过: ${articleId}`);
            continue;
          }

          // 构建请求 URL
          const detailUrl = endpoint.url.replace('{id}', articleId);

          logger.debug(`正在抓取文章详情: ${articleId}`);

          // 发送请求
          const response = await fetch(detailUrl, {
            method: endpoint.method,
            headers: endpoint.headers
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          let data: any = await response.json();

          // 4. 复用处理ForesightNews压缩数据的逻辑
          data = this.parseCompressedProperties(data);
          if (!Array.isArray(data.data)) {
            data.data = [data.data];
          }
          // 5. 解析数据并保存到数据库
          const items = await this.parseResponseData(data, source, 'articleDetail', endpoint.parser);

          if (items && items.length > 0) {
            // 保存到数据库
            for (const item of items) {
              await this.itemsCollection.updateOne(
                { source: item.source, dataType: item.dataType, id: item.id },
                { $set: item },
                { upsert: true }
              );
              const newArticle = { ...article, articleContent: item.content }
              await this.itemsCollection.updateOne(
                { source: source, dataType: 'article', id: article.id },
                { $set: newArticle },
                { upsert: true }
              );
              console.log(`update article: ${article.id} succeed!`)
            }
            successCount++;
            logger.debug(`文章详情抓取成功: ${articleId}`);
          } else {
            logger.warn(`文章详情解析失败: ${articleId}`);
            errorCount++;
          }

          // 添加延迟避免请求过快
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          logger.error(`抓取文章详情失败 ${article.id}:`, error);
          errorCount++;
        }
      }

      logger.info(`文章详情抓取完成: 成功 ${successCount} 篇，失败 ${errorCount} 篇`);

    } catch (error) {
      logger.error('抓取文章详情过程中发生错误:', error);
      throw error;
    }
  }
}