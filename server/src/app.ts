import express, { Application } from 'express';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import 'dotenv/config';

import { errorHandler } from '@/middlewares/error';
import { requestLogger } from '@/middlewares/logger';

import newsRoutes from '@/routes/news';
import articleRoutes from '@/routes/article';
import feedRoutes from '@/routes/feed';
import flashRoutes from '@/routes/flash';
import pricesRoutes from '@/routes/prices';
import tweetsRoutes from '@/routes/tweets';
import financingRoutes from '@/routes/financing';


import { logger } from '@/utils/logger';
import { cacheManager } from '@/utils/cache';

class App {
  public app: Application;
  public port: number;

  constructor() {
    this.app = express();
    this.port = parseInt(process.env.PORT || '3000', 10);

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    // this.app.use(helmet({
    //   contentSecurityPolicy: {
    //     directives: {
    //       defaultSrc: ["'self'"],
    //       styleSrc: ["'self'", "'unsafe-inline'"],
    //       scriptSrc: ["'self'"],
    //       imgSrc: ["'self'", "data:", "https:"],
    //     },
    //   },
    //   crossOriginEmbedderPolicy: false,
    // }));

    const corsOptions = {
      origin: process.env.CORS_ORIGIN?.split(',') || [
        'http://localhost:3000',
        'http://localhost:8081',
      ],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
      optionsSuccessStatus: 200,
    };
    this.app.use(cors(corsOptions));

    this.app.use(compression());

    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '600000', 10), // 6 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10), // limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10) / 1000),
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api', limiter);

    this.app.use(requestLogger);

    if (process.env.TRUST_PROXY === 'true') {
      this.app.set('trust proxy', 1);
    }
  }

  /**
   * Initialize API routes
   */
  private initializeRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // API routes
    this.app.use('/api/news', newsRoutes);
    this.app.use('/api/article', articleRoutes);
    this.app.use('/api/feed', feedRoutes);
    this.app.use('/api/flash', flashRoutes);
    this.app.use('/api/prices', pricesRoutes);
    this.app.use('/api/tweets', tweetsRoutes); // 推特相关路由
    this.app.use('/api/financing', financingRoutes); // 融资信息路由


    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Route not found',
        message: `The requested route ${req.originalUrl} does not exist`,
        availableRoutes: [
          '/health',
          '/api',
          '/api/news',
          '/api/feed',
          '/api/flash',
          '/api/prices',
          '/api/tweets',
          '/api/financing',
          '/api/news/categories',
          '/api/news/search',
        ],
      });
    });
  }

  /**
   * Initialize error handling
   */
  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  /**
   * Start the Express server
   */
  public listen(): void {
    this.app.listen(this.port, () => {
      logger.info(`🚀 ChainMix Backend Server started on port ${this.port}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🔗 Health check: http://localhost:${this.port}/health`);
      logger.info(`📚 API documentation: http://localhost:${this.port}/api`);

      // Initialize cache cleanup
      cacheManager.startCleanupTimer();

      logger.info('✅ ChainMix Backend Server is ready to serve API requests');
    });
  }

  /**
   * Graceful shutdown handler
   */
  public setupGracefulShutdown(): void {
    const shutdown = (signal: string): void => {
      logger.info(`🛑 Received ${signal}, starting graceful shutdown...`);

      // Stop accepting new connections
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error: Error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }
}

const app = new App();
app.setupGracefulShutdown();
app.listen();

export default app.app;
