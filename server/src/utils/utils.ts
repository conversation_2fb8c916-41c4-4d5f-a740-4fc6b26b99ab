import crypto from 'crypto'

export function createDateIterator(startDateStr: string, endDateStr: string) {
  let year = '', month = '', day = '';
  for (let i = 0; i < startDateStr.length; i++) {
    if (i < 4) year += startDateStr[i];
    else if (i < 6) month += startDateStr[i];
    else if (i < 8) day += startDateStr[i];
  }
  const startDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

  year = '', month = '', day = '';
  for (let i = 0; i < endDateStr.length; i++) {
    if (i < 4) year += endDateStr[i];
    else if (i < 6) month += endDateStr[i];
    else if (i < 8) day += endDateStr[i];
  }
  const endDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

  let currentDate = new Date(startDate);

  return {
    next: function () {
      if (currentDate <= endDate) {
        const formattedDate = `${currentDate.getFullYear()}${String(currentDate.getMonth() + 1).padStart(2, '0')}${String(currentDate.getDate()).padStart(2, '0')}`;

        currentDate.setDate(currentDate.getDate() + 1);

        return formattedDate;
      } else {
        return null;
      }
    }
  };
}

export function stringToHexHash(str: string): string {
  const hash = crypto.createHash('md5').update(str).digest('hex');
  return hash.slice(0, 24);
}