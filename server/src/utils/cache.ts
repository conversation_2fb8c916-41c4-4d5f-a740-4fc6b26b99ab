import NodeCache from 'node-cache';
import { logger, logHelpers } from '@/utils/logger';

/**
 * Cache Manager class for handling in-memory caching with TTL
 */
class CacheManager {
  private cache: NodeCache;
  private readonly defaultTTL: number;
  private readonly checkPeriod: number;

  constructor() {
    // Get TTL from environment or default to 5 minutes
    this.defaultTTL = parseInt(process.env.CACHE_TTL_MINUTES || '5', 10) * 60;
    
    // Get check period from environment or default to 10 minutes
    this.checkPeriod = parseInt(process.env.CACHE_CHECK_PERIOD_MINUTES || '10', 10) * 60;

    // Initialize NodeCache with configuration
    this.cache = new NodeCache({
      stdTTL: this.defaultTTL,
      checkperiod: this.checkPeriod,
      useClones: false, // Better performance, but be careful with object mutations
      deleteOnExpire: true,
    });

    // Set up event listeners for cache operations
    this.setupEventListeners();

    logger.info('Cache Manager initialized', {
      defaultTTL: this.defaultTTL,
      checkPeriod: this.checkPeriod,
    });
  }

  /**
   * Set up event listeners for cache operations
   */
  private setupEventListeners(): void {
    this.cache.on('set', (key: string, _: unknown) => {
      logHelpers.logCacheOperation('set', key);
    });

    this.cache.on('del', (key: string, _: unknown) => {
      logHelpers.logCacheOperation('delete', key);
    });

    this.cache.on('expired', (key: string, _: unknown) => {
      logHelpers.logCacheOperation('delete', key);
      logger.debug(`Cache key expired: ${key}`);
    });

    this.cache.on('flush', () => {
      logHelpers.logCacheOperation('clear', 'all');
      logger.debug('Cache flushed');
    });
  }

  /**
   * Get value from cache
   */
  public get<T>(key: string): T | undefined {
    const value = this.cache.get<T>(key);
    
    if (value !== undefined) {
      logHelpers.logCacheOperation('hit', key);
      return value;
    } else {
      logHelpers.logCacheOperation('miss', key);
      return undefined;
    }
  }

  /**
   * Set value in cache with optional TTL
   */
  public set<T>(key: string, value: T, ttl?: number): boolean {
    const cacheKey = this.normalizeKey(key);
    const timeToLive = ttl || this.defaultTTL;
    
    const success = this.cache.set(cacheKey, value, timeToLive);
    
    if (success) {
      logHelpers.logCacheOperation('set', cacheKey, timeToLive);
    } else {
      logger.warn(`Failed to set cache key: ${cacheKey}`);
    }
    
    return success;
  }

  /**
   * Delete value from cache
   */
  public delete(key: string): number {
    const cacheKey = this.normalizeKey(key);
    const deletedCount = this.cache.del(cacheKey);
    
    if (deletedCount > 0) {
      logHelpers.logCacheOperation('delete', cacheKey);
    }
    
    return deletedCount;
  }

  /**
   * Check if key exists in cache
   */
  public has(key: string): boolean {
    const cacheKey = this.normalizeKey(key);
    return this.cache.has(cacheKey);
  }

  /**
   * Get TTL for a key
   */
  public getTTL(key: string): number | undefined {
    const cacheKey = this.normalizeKey(key);
    return this.cache.getTtl(cacheKey);
  }

  /**
   * Get all keys in cache
   */
  public keys(): string[] {
    return this.cache.keys();
  }

  /**
   * Get cache statistics
   */
  public getStats(): NodeCache.Stats {
    return this.cache.getStats();
  }

  /**
   * Clear all cache entries
   */
  public clear(): void {
    this.cache.flushAll();
  }

  /**
   * Get multiple values from cache
   */
  public getMultiple<T>(keys: string[]): Record<string, T> {
    const normalizedKeys = keys.map(key => this.normalizeKey(key));
    return this.cache.mget<T>(normalizedKeys);
  }

  /**
   * Set multiple values in cache
   */
  public setMultiple<T>(keyValuePairs: Array<{ key: string; value: T; ttl?: number }>): boolean {
    const normalizedPairs = keyValuePairs.map(pair => ({
      key: this.normalizeKey(pair.key),
      val: pair.value,
      ttl: pair.ttl || this.defaultTTL,
    }));

    const success = this.cache.mset(normalizedPairs);
    
    if (success) {
      normalizedPairs.forEach(pair => {
        logHelpers.logCacheOperation('set', pair.key, pair.ttl);
      });
    }
    
    return success;
  }

  /**
   * Get or set pattern - get value, or compute and cache if not exists
   */
  public async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    const cacheKey = this.normalizeKey(key);
    const cachedValue = this.get<T>(cacheKey);
    
    if (cachedValue !== undefined) {
      return cachedValue;
    }
    
    // Value not in cache, compute it
    const value = await factory();
    this.set(cacheKey, value, ttl);
    
    return value;
  }

  /**
   * Normalize cache key to prevent issues
   */
  private normalizeKey(key: string): string {
    // Replace special characters and spaces, convert to lowercase
    return key
      .replace(/[^a-zA-Z0-9_-]/g, '_')
      .toLowerCase()
      .substring(0, 250); // Limit key length
  }

  /**
   * Start periodic cleanup timer
   */
  public startCleanupTimer(): void {
    // NodeCache handles this automatically, but we can add custom cleanup logic
    setInterval(() => {
      const stats = this.getStats();
      logger.debug('Cache statistics', {
        keys: stats.keys,
        hits: stats.hits,
        misses: stats.misses,
        hitRate: stats.hits / (stats.hits + stats.misses) || 0,
      });
    }, this.checkPeriod * 1000);
  }

  /**
   * Close cache and cleanup resources
   */
  public close(): void {
    this.cache.close();
    logger.info('Cache Manager closed');
  }
}

// Create singleton instance
export const cacheManager = new CacheManager();

// Export utility functions for cache key generation
export const cacheKeys = {
  news: {
    articles: (page: number, limit: number, category?: string): string =>
      `news:articles:${page}:${limit}:${category || 'all'}`,
    article: (id: string): string => `news:article:${id}`,
    categories: (): string => 'news:categories',
    search: (query: string, page: number, limit: number): string =>
      `news:search:${encodeURIComponent(query)}:${page}:${limit}`,
    trending: (limit: number): string => `news:trending:${limit}`,
    featured: (limit: number): string => `news:featured:${limit}`,
  },
  prices: {
    coins: (page: number, limit: number, currency: string): string =>
      `prices:coins:${page}:${limit}:${currency}`,
    coin: (id: string, currency: string): string => `prices:coin:${id}:${currency}`,
    history: (id: string, days: string, currency: string): string =>
      `prices:history:${id}:${days}:${currency}`,
    trending: (): string => 'prices:trending',
  },
  twitter: {
    posts: (page: number, limit: number): string => `twitter:posts:${page}:${limit}`,
  },
};

export default cacheManager;