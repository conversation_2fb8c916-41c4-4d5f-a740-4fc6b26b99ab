# Flash 命令使用指南

## 概述

Flash 命令是 ChainMix 数据抓取系统中专门用于抓取所有数据源的 flash 快讯数据的命令。它支持串行和并发两种抓取模式，可以高效地从多个区块链新闻源获取最新的快讯信息。

## 支持的数据源

Flash 命令支持以下数据源的快讯数据：

- **chaincatcher** - ChainCatcher 快讯
- **chainfeeds** - ChainFeeds 快讯  
- **followin** - Followin 推荐新闻
- **odaily** - Odaily 快讯消息
- **techflow** - TechFlow 快讯消息
- **theblockbeats** - 律动BlockBeats 快讯
- **panewslab** - PANews 快讯消息

## 使用方法

### 基本用法

```bash
# 抓取所有数据源的flash数据（串行模式）
npm run flash

# 使用并发模式抓取（推荐，速度更快）
npm run flash -- --concurrent

# 强制全量抓取
npm run flash -- --force

# 限制抓取页数
npm run flash -- --max-pages 5
```

### 高级用法

```bash
# 只抓取指定数据源
npm run flash -- --sources "chaincatcher,odaily,techflow"

# 并发模式 + 强制全量 + 限制页数
npm run flash -- --concurrent --force --max-pages 3

# 只抓取单个数据源
npm run flash -- --sources "chaincatcher"
```

### 命令选项

- `-f, --force` - 强制全量抓取，忽略增量抓取逻辑
- `-m, --max-pages <pages>` - 最大抓取页数，0表示无限制（默认：0）
- `-c, --concurrent` - 使用并发模式抓取，速度更快（默认：false）
- `-s, --sources <sources>` - 指定要抓取的数据源，用逗号分隔（默认：所有支持的源）

## 抓取模式

### 串行模式（默认）
- 逐个数据源进行抓取
- 资源占用较低
- 适合服务器资源有限的情况

### 并发模式（推荐）
- 多个数据源同时抓取
- 速度更快，效率更高
- 适合服务器资源充足的情况

## 输出示例

### 串行模式输出
```
🚀 开始抓取flash快讯数据
目标数据源: chaincatcher, chainfeeds, followin, odaily, techflow, theblockbeats, panewslab
配置: 强制全量=false, 最大页数=无限制, 并发模式=false
📰 开始抓取 chaincatcher 的flash数据
✅ chaincatcher flash数据抓取完成: 25 项
📰 开始抓取 chainfeeds 的flash数据
✅ chainfeeds flash数据抓取完成: 18 项
...

=== 🎯 Flash数据抓取完成 ===
总耗时: 45.2s
成功源: 7/7
失败源: 0
成功率: 100.0%
```

### 并发模式输出
```
🚀 开始抓取flash快讯数据
目标数据源: chaincatcher, chainfeeds, followin, odaily, techflow, theblockbeats, panewslab
配置: 强制全量=false, 最大页数=无限制, 并发模式=true
📡 使用并发模式抓取flash数据

=== 🎯 Flash数据抓取完成 ===
总耗时: 12.5s
成功率: 100.0%
成功源: 7/7

📊 各数据源详情:
  ✅ chaincatcher: 成功 (3.2s)
    抓取项目: 25 项
  ✅ chainfeeds: 成功 (4.1s)
    抓取项目: 18 项
  ✅ followin: 成功 (2.8s)
    抓取项目: 12 项
  ...
```

## 注意事项

1. **数据库连接**: 每个数据源使用独立的数据库，命名格式为 `{source}_db`
2. **错误处理**: 单个数据源失败不会影响其他数据源的抓取
3. **增量抓取**: 默认使用增量抓取，只获取新数据，使用 `--force` 可强制全量抓取
4. **并发限制**: 并发模式下最多同时抓取5个数据源，避免过度占用资源

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 MongoDB 服务是否运行
   - 验证连接字符串配置

2. **某个数据源抓取失败**
   - 查看日志中的具体错误信息
   - 检查网络连接和API可用性

3. **抓取速度慢**
   - 使用 `--concurrent` 选项启用并发模式
   - 使用 `--max-pages` 限制抓取页数

### 日志查看

抓取过程中的详细日志会输出到控制台，包括：
- 每个数据源的抓取进度
- 成功/失败状态
- 抓取的数据项数量
- 错误信息（如有）

## 相关命令

- `npm run scraper stats` - 查看抓取统计信息
- `npm run scraper query -- --type flash` - 查询flash数据
- `npm run scraper search-all -- --keyword "关键词" --types flash` - 搜索flash数据
