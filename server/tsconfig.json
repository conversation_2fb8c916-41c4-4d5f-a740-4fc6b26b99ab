{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "downlevelIteration": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "pretty": true, "typeRoots": ["./node_modules/@types"], "types": ["node", "jest"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/types": ["./types"], "@/services": ["./services"], "@/controllers": ["./controllers"], "@/middlewares": ["./middlewares"], "@/utils": ["./utils"], "@/routes": ["./routes"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"], "transpileOnly": true, "files": true}}