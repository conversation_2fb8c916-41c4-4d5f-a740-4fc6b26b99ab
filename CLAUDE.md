# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ChainMix (智能区块链资讯平台) is a cross-platform React Native mobile application built with Expo SDK 53. It provides blockchain and cryptocurrency news with AI-powered analysis. The app follows a modern architecture with both frontend (React Native) and backend (Node.js/Express/TypeScript) components.

## Development Commands

### Frontend (React Native/Expo)
```bash
# Development
npm start              # Start Expo dev server with tunnel
npm run dev:web        # Start web development server
npm run ios            # Run on iOS simulator
npm run android        # Run on Android emulator
npm run web            # Run web version

# Building
npm run build:android           # Build Android with EAS (local)
npm run build:android-apk      # Build production APK
npm run build:android-preview  # Build preview APK (local)
npm run build:ios             # Build iOS with EAS (local)
```

### Backend Server
```bash
# In server/ directory
npm run dev      # Development with hot reload (ts-node-dev)
npm start        # Production start (requires build)
npm run build    # TypeScript compilation
npm test         # Run Jest tests
npm run test:watch    # Watch mode tests
npm run test:coverage # Coverage report
npm run lint     # ESLint check
npm run lint:fix # ESLint auto-fix
npm run format   # Prettier formatting

# Data Scraping (Concurrent & Fast)
npm run scraper:all              # 🚀 Concurrent scraping of all sources (RECOMMENDED)
npm run scraper:all-sequential   # Sequential scraping (slower, legacy)
npm run scraper:init             # Initialize scraper configurations
npm run scraper:stats            # Single database statistics
npm run scraper:stats-all        # Cross-source statistics from all databases
npm run scraper:search -- -k "keyword"  # Search across all data sources
npm run scraper:interactive      # Interactive mode

# Advanced Scraper CLI Usage
npm run scraper scrape-concurrent --help           # Show concurrent scraping options
npm run scraper scrape-concurrent --force          # Force full scrape
npm run scraper scrape-concurrent --concurrent-sources 5 --concurrent-per-source 3  # Custom concurrency
npm run scraper scrape-concurrent --exclude "source1,source2"  # Exclude specific sources
npm run scraper search-all -k "Bitcoin" -s "chaincatcher,odaily" -l 100  # Advanced search
```

## Architecture

### Frontend Structure
- **Framework**: React Native + Expo SDK 53, TypeScript
- **UI**: Tamagui for design system, custom components
- **Navigation**: React Navigation v7 (stack + bottom tabs)
- **State Management**: Zustand with persistence (AsyncStorage)
- **API Client**: Axios with intelligent caching and offline fallback
- **Development Tools**: Metro bundler, EAS Build

### Backend Structure
- **Runtime**: Node.js + Express + TypeScript
- **Data Sources**: Web scraping + MongoDB for caching
- **Services**: News aggregation, price data, flash news, trending analysis
- **Middleware**: Compression, CORS, rate limiting, logging
- **Scheduler**: Automated data fetching and updates
- **Scraping Architecture**: 
  - **Concurrent Scraper** (NEW): Multi-threaded concurrent scraping across independent databases
  - **Legacy Scraper**: Sequential scraping to single database
  - **Database Design**: Each API source has its own dedicated MongoDB database for isolation and performance

### Key Architectural Patterns

1. **Dual API Strategy**: Frontend can work with real backend or fall back to mock data in development
2. **Multi-layer Caching**: Memory cache + AsyncStorage + server-side cache
3. **Theme System**: Complete dark/light theme support across all components
4. **Error Boundaries**: Comprehensive error handling and graceful degradation
5. **Modular Services**: Separate services for news, scraping, fetching, scheduling

## API Integration

The frontend connects to backend at:
- Development: `http://localhost:3001/api`
- Production: `https://api.chainmix.com/api`

API automatically falls back to mock data if backend is unavailable (see `src/services/api.ts:233`).

### New Concurrent Scraper API Endpoints

**Core Content APIs (v2) - Multi-Database Data Fusion:**
- `/api/news-v2/*` - Aggregated news from all sources with intelligent filtering
- `/api/feed-v2/*` - Smart content feeds with recommendation algorithms
- `/api/flash-v2/*` - Real-time breaking news with urgency scoring
- `/api/prices-v2/*` - Multi-source cryptocurrency prices with technical analysis

**Advanced Analytics APIs:**
- `/api/analytics/trending-keywords` - Hot topics and keyword trends
- `/api/analytics/hot-topics` - Event-based topic clustering  
- `/api/analytics/market-sentiment` - Cross-source sentiment analysis
- `/api/analytics/content-quality` - Data quality monitoring
- `/api/analytics/data-freshness` - Source freshness analysis
- `/api/analytics/source-comparison` - Multi-criteria source comparison

**Aggregated Data APIs:**
- `/api/aggregated/dashboard` - Comprehensive dashboard data
- `/api/aggregated/timeline` - Time-ordered mixed content view
- `/api/aggregated/digest` - Daily/weekly content summaries
- `/api/aggregated/cross-reference` - Cross-source event analysis
- `/api/aggregated/real-time` - Live data streaming
- `/api/aggregated/health` - System health monitoring

**Concurrent Scraper Management:**
- `/api/concurrent-scraper/stats` - Multi-database statistics
- `/api/concurrent-scraper/search` - Cross-database search
- `/api/concurrent-scraper/health` - Database health check

## Database & Data Sources

The backend aggregates news from multiple sources with **two scraping architectures**:

### Concurrent Scraper (Recommended)
- **Independent Databases**: Each API source has its own MongoDB database
- **Concurrent Processing**: Multiple sources scraped simultaneously with configurable concurrency
- **Database Isolation**: `chaincatcher_db`, `chainfeeds_db`, `followin_db`, `odaily_db`, etc.
- **Performance**: 3-5x faster than sequential scraping
- **Fault Tolerance**: One source failure doesn't affect others
- **API Routes**: `/api/concurrent-scraper/*`

### Legacy Scraper
- **Single Database**: All data stored in `chainmix` database
- **Sequential Processing**: One source/endpoint at a time
- **API Routes**: `/api/accurate-scraper/*`

### Data Sources
- Various blockchain news websites (scraped via `server/src/scraper/` modules)
- Real-time price data
- Social media trends (Twitter integration)
- Flash news updates

**API Sources with Independent Databases:**
1. **ChainCatcher** (`chaincatcher_db`) - Articles, Flash News
2. **ChainFeeds** (`chainfeeds_db`) - Feeds, Flash, Topics, Subjects
3. **Followin** (`followin_db`) - Flash News
4. **Odaily** (`odaily_db`) - Depth Articles, Flash, Posts, Prices, Tweets
5. **TechFlow** (`techflow_db`) - Flash News
6. **TrendX** (`trendx_db`) - Financing, News, Tweets
7. **TheBlockBeats** (`theblockbeats_db`) - Flash, Finance
8. **PANews** (`panewslab_db`) - Flash News
9. **ForesightNews** (`foresightnews_db`) - All content types

## Development Workflow

1. **Frontend Development**: Start with `npm start` - works independently with mock data
2. **Full Stack**: Run backend with `cd server && npm run dev`, then frontend
3. **API Development**: Backend routes in `server/src/routes/`, controllers in `server/src/controllers/`
4. **UI Components**: Custom components in `src/components/ui/`, follow existing patterns
5. **State Management**: Use Zustand stores in `src/store/index.ts`
6. **Data Scraping**: Use `npm run scraper:all` for concurrent scraping or `npm run scraper:all-sequential` for legacy mode

## Testing

- **Frontend**: No test setup currently (tests would use React Native Testing Library)
- **Backend**: Jest configured with `npm test` in server directory
- **API Testing**: Use `src/screens/Test/APITestScreen.tsx` for endpoint testing

## Key Files to Understand

- `App.tsx` - Main app entry with providers and navigation
- `src/store/index.ts` - Global state management (app + news stores)
- `src/services/api.ts` - Complete API service with caching and fallbacks
- `server/src/app.ts` - Backend application setup
- `server/src/fetcher/` - Core data fetching and aggregation logic
- `server/src/scraper/` - Web scraping utilities and parsers
- **NEW Concurrent Scraping Files**:
  - `server/src/scraper/concurrent-scraper.ts` - Multi-database concurrent scraper
  - `server/src/routes/concurrent-scraper.ts` - Concurrent scraper API routes
  - `server/src/scraper/cli.ts` - Enhanced CLI with concurrent commands
- **NEW API Routes** (using multi-database data fusion):
  - `server/src/routes/news-v2.ts` - Enhanced news API with cross-source aggregation
  - `server/src/routes/feed-v2.ts` - Smart recommendation feeds
  - `server/src/routes/flash-v2.ts` - Real-time breaking news with urgency scoring
  - `server/src/routes/prices-v2.ts` - Multi-source price aggregation with technical analysis
  - `server/src/routes/analytics.ts` - Advanced analytics and trending analysis
  - `server/src/routes/aggregated.ts` - Dashboard and cross-reference APIs

## Package Management

Uses `pnpm` as package manager. Lock files are committed for both frontend and backend.