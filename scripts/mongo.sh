#!/bin/bash

# MongoDB Docker启动脚本
# 用于ChainMix项目的MongoDB数据库服务

set -e

# 配置变量
MONGO_CONTAINER_NAME="chainmix-mongodb"
MONGO_PORT="27017"
MONGO_DATA_DIR="./data/mongodb"
MONGO_LOG_DIR="./logs/mongodb"
MONGO_VERSION="7.0"
MONGO_DATABASE="chainmix"
MONGO_USERNAME="chainmix_user"
MONGO_PASSWORD="chainmix_password_2025"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi

    log_info "Docker检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建数据和日志目录..."

    mkdir -p "$MONGO_DATA_DIR"
    mkdir -p "$MONGO_LOG_DIR"

    # 设置目录权限
    chmod 755 "$MONGO_DATA_DIR"
    chmod 755 "$MONGO_LOG_DIR"

    log_info "目录创建完成: $MONGO_DATA_DIR, $MONGO_LOG_DIR"
}

# 停止并删除现有容器
cleanup_existing_container() {
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${MONGO_CONTAINER_NAME}$"; then
        log_warn "发现现有容器 $MONGO_CONTAINER_NAME，正在停止并删除..."

        docker stop "$MONGO_CONTAINER_NAME" 2>/dev/null || true
        docker rm "$MONGO_CONTAINER_NAME" 2>/dev/null || true

        log_info "现有容器已清理"
    fi
}

# 启动MongoDB容器
start_mongodb() {
    log_info "启动MongoDB容器..."

    docker run -d \
        --name "$MONGO_CONTAINER_NAME" \
        --restart unless-stopped \
        -p "$MONGO_PORT:27017" \
        -v "$(pwd)/$MONGO_DATA_DIR:/data/db" \
        -v "$(pwd)/$MONGO_LOG_DIR:/var/log/mongodb" \
        -e MONGO_INITDB_ROOT_USERNAME="$MONGO_USERNAME" \
        -e MONGO_INITDB_ROOT_PASSWORD="$MONGO_PASSWORD" \
        -e MONGO_INITDB_DATABASE="$MONGO_DATABASE" \
        --health-cmd="mongosh --eval 'db.adminCommand(\"ping\")'" \
        --health-interval=10s \
        --health-timeout=5s \
        --health-retries=5 \
        mongo:"$MONGO_VERSION"

    if [ $? -eq 0 ]; then
        log_info "MongoDB容器启动成功"
    else
        log_error "MongoDB容器启动失败"
        exit 1
    fi
}

# 等待MongoDB就绪
wait_for_mongodb() {
    log_info "等待MongoDB服务就绪..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if docker exec "$MONGO_CONTAINER_NAME" mongosh --quiet --eval "db.adminCommand('ping')" &>/dev/null; then
            log_info "MongoDB服务已就绪"
            return 0
        fi

        log_debug "等待MongoDB启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    log_error "MongoDB启动超时"
    return 1
}

# 初始化数据库和集合
initialize_database() {
    log_info "初始化数据库和集合..."

    # 创建初始化脚本
    cat > /tmp/mongo_init.js << EOF
// 切换到chainmix数据库
use chainmix;

// 创建API数据集合
db.createCollection('api_definitions', {
    validator: {
        \$jsonSchema: {
            bsonType: 'object',
            required: ['source', 'endpoint', 'method', 'url'],
            properties: {
                source: { bsonType: 'string', description: '数据源名称' },
                endpoint: { bsonType: 'string', description: '端点名称' },
                method: { bsonType: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE'] },
                url: { bsonType: 'string', description: 'API URL' },
                headers: { bsonType: 'object', description: '请求头' },
                params: { bsonType: 'object', description: '请求参数' },
                body: { bsonType: ['object', 'null'], description: '请求体' },
                response_example: { bsonType: 'object', description: '响应示例' },
                created_at: { bsonType: 'date', description: '创建时间' },
                updated_at: { bsonType: 'date', description: '更新时间' }
            }
        }
    }
});

// 创建抓取数据集合
db.createCollection('scraped_data', {
    validator: {
        \$jsonSchema: {
            bsonType: 'object',
            required: ['source', 'data_type', 'data'],
            properties: {
                source: { bsonType: 'string', description: '数据源' },
                data_type: { bsonType: 'string', description: '数据类型' },
                data: { bsonType: 'object', description: '抓取的数据' },
                scraped_at: { bsonType: 'date', description: '抓取时间' },
                metadata: { bsonType: 'object', description: '元数据' }
            }
        }
    }
});

// 创建索引
db.api_definitions.createIndex({ 'source': 1, 'endpoint': 1 }, { unique: true });
db.api_definitions.createIndex({ 'created_at': -1 });
db.scraped_data.createIndex({ 'source': 1, 'data_type': 1 });
db.scraped_data.createIndex({ 'scraped_at': -1 });

print('数据库初始化完成');
EOF

    # 执行初始化脚本
    docker exec -i "$MONGO_CONTAINER_NAME" mongosh -u "$MONGO_USERNAME" -p "$MONGO_PASSWORD" --authenticationDatabase admin < /tmp/mongo_init.js

    if [ $? -eq 0 ]; then
        log_info "数据库初始化完成"
        rm -f /tmp/mongo_init.js
    else
        log_error "数据库初始化失败"
        rm -f /tmp/mongo_init.js
        exit 1
    fi
}

# 显示连接信息
show_connection_info() {
    log_info "MongoDB连接信息:"
    echo "  容器名称: $MONGO_CONTAINER_NAME"
    echo "  端口: $MONGO_PORT"
    echo "  数据库: $MONGO_DATABASE"
    echo "  用户名: $MONGO_USERNAME"
    echo "  密码: $MONGO_PASSWORD"
    echo ""
    echo "连接字符串:"
    echo "  ***************************************************:$MONGO_PORT/$MONGO_DATABASE?authSource=admin"
    echo ""
    echo "管理命令:"
    echo "  查看容器状态: docker ps | grep $MONGO_CONTAINER_NAME"
    echo "  查看容器日志: docker logs $MONGO_CONTAINER_NAME"
    echo "  进入MongoDB Shell: docker exec -it $MONGO_CONTAINER_NAME mongosh -u $MONGO_USERNAME -p $MONGO_PASSWORD --authenticationDatabase admin"
    echo "  停止容器: docker stop $MONGO_CONTAINER_NAME"
    echo "  启动容器: docker start $MONGO_CONTAINER_NAME"
}

# 主函数
main() {
    log_info "开始启动MongoDB Docker容器..."

    check_docker
    create_directories
    cleanup_existing_container
    start_mongodb

    if wait_for_mongodb; then
        initialize_database
        show_connection_info
        log_info "MongoDB启动完成！"
    else
        log_error "MongoDB启动失败"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-start}" in
    start)
        main
        ;;
    stop)
        log_info "停止MongoDB容器..."
        docker stop "$MONGO_CONTAINER_NAME" 2>/dev/null || log_warn "容器可能已经停止"
        log_info "MongoDB容器已停止"
        ;;
    restart)
        log_info "重启MongoDB容器..."
        docker restart "$MONGO_CONTAINER_NAME" 2>/dev/null || log_error "重启失败，容器可能不存在"
        log_info "MongoDB容器已重启"
        ;;
    status)
        if docker ps --format 'table {{.Names}}\t{{.Status}}' | grep -q "^${MONGO_CONTAINER_NAME}"; then
            log_info "MongoDB容器正在运行"
            docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep "$MONGO_CONTAINER_NAME"
        else
            log_warn "MongoDB容器未运行"
        fi
        ;;
    logs)
        docker logs -f "$MONGO_CONTAINER_NAME"
        ;;
    shell)
        docker exec -it "$MONGO_CONTAINER_NAME" mongosh -u "$MONGO_USERNAME" -p "$MONGO_PASSWORD" --authenticationDatabase admin
        ;;
    clean)
        log_warn "这将删除MongoDB容器和所有数据！"
        read -p "确认删除? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker stop "$MONGO_CONTAINER_NAME" 2>/dev/null || true
            docker rm "$MONGO_CONTAINER_NAME" 2>/dev/null || true
            rm -rf "$MONGO_DATA_DIR" "$MONGO_LOG_DIR"
            log_info "MongoDB容器和数据已清理"
        else
            log_info "操作已取消"
        fi
        ;;
    help|--help|-h)
        echo "MongoDB Docker管理脚本"
        echo ""
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start    启动MongoDB容器 (默认)"
        echo "  stop     停止MongoDB容器"
        echo "  restart  重启MongoDB容器"
        echo "  status   查看容器状态"
        echo "  logs     查看容器日志"
        echo "  shell    进入MongoDB Shell"
        echo "  clean    删除容器和数据"
        echo "  help     显示此帮助信息"
        ;;
    *)
        log_error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac